<template>
  <CardDialog
    v-model="activeDialog"
    :title="`Duplication d'une ${operation.stepId === 0 ? 'simulation' : 'opération'}${
      operation.headOperation ? ' chapeau' : ''
    }`"
    width="40%"
  >
    <VTextField v-model="copyName" label="Nom de la copie" />
    <VSwitch
      v-if="props.operation.stepId !== 0"
      v-model="regroupCopy"
      :label="
        operation.operationsGroup
          ? 'Ajouter la copie au même regroupement que l\'originale'
          : 'Voulez-vous créer un regroupement à partir de ces deux opérations?'
      "
    />
    <template v-if="regroupCopy && !operation.operationsGroup">
      <VForm ref="formRef">
        <VRow class="flex-column my-n2">
          <VCol>
            <NjExpansionPanel title="Regroupement">
              <VRow class="flex-column">
                <VCol>
                  <VTextField v-model="name" label="Nom du regroupement" :rules="[requiredRule]" />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VCol>
            <NjExpansionPanel title="Bénéficiaire">
              <VRow>
                <VCol>
                  <BeneficiaryDisplayValue
                    :with-color="operation.stepId >= 30"
                    :model-value="operation.beneficiary!"
                    :rules="[requiredRule]"
                    :legacy="operation.legacyBeneficiary"
                  />
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VCol>
          <VCol>
            <NjExpansionPanel title="Organisation">
              <NjDisplayValue label="Nom" :value="operation.entity.name" />
            </NjExpansionPanel>
          </VCol>
        </VRow>
      </VForm>
    </template>
    <template #actions>
      <NjBtn variant="outlined" @click="activeDialog = false"> Annuler </NjBtn>
      <NjBtn
        color="primary"
        @click="regroupCopy && !operation.operationsGroup ? createOperationGroup() : duplicateSimulation()"
      >
        Valider
      </NjBtn>
    </template>
  </CardDialog>
</template>
<script lang="ts" setup>
import BeneficiaryDisplayValue from '@/components/BeneficiaryDisplayValue.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import router from '@/router'
import { useSnackbarStore } from '@/stores/snackbar'
import type { OperationsGroup, OperationsGroupRequest } from '@/types/operationsGroup'
import { requiredRule } from '@/types/rule'
import type { Operation, OperationRequest } from '@/types/operation'
import type { PropType } from 'vue'
import type { VForm } from 'vuetify/components'

const props = defineProps({
  modelValue: Boolean,
  operation: {
    type: Object as PropType<Operation>,
    default: makeEmptyOperation,
  },
  disableUnsavedData: Function,
})

const emit = defineEmits(['update:model-value', 'update:operation'])

const snackbar = useSnackbarStore()

const activeDialog = computed<boolean>({
  get() {
    return props.modelValue
  },
  set(v) {
    emit('update:model-value', v)
  },
})

const regroupCopy = ref(false)
const copyName = ref(
  props.operation.stepId === 0
    ? props.operation.simulationName + ' - copie'
    : props.operation.operationName + ' - copie'
)

watch(
  activeDialog,
  () =>
    (copyName.value =
      props.operation.stepId === 0
        ? props.operation.simulationName + ' - copie'
        : props.operation.operationName + ' - copie')
)

const duplicateSimulation = async () => {
  const operationGroupId = regroupCopy.value
    ? props.operation.operationsGroup
      ? props.operation.operationsGroup!.id
      : operationsGroup.value.value!.id
    : 0

  if (regroupCopy.value && !props.operation.operationsGroup) {
    await simulationApi
      .updateSimulation(props.operation.id, {
        ...mapToOperationRequest(props.operation),
        operationsGroupId: operationGroupId,
      })
      .catch(async (err) =>
        snackbar.setError(
          await handleAxiosException(
            err,
            (r) => r.data?.message ?? "Une erreur est survenue lors de la modification de l'originale"
          )
        )
      )
  }

  const request: OperationRequest = {
    ...mapToOperationRequest(props.operation),
    simulationName: props.operation.simulationName ? copyName.value : '',
    operationName: props.operation.stepId !== 0 ? copyName.value : '',
    standardizedOperationSheetId: props.operation.standardizedOperationSheet.id,
    operationsGroupId: operationGroupId,
    stepId: props.operation.stepId > 20 ? 20 : props.operation.stepId,
    duplicateChronoCode: props.operation.chronoCode,
    signedDate: null,
    actualEndWorksDate: null,
    atypicalClassicValuationValue: props.operation.headOperation
      ? (props.operation.atypicalClassicValuationValue ?? props.operation.classicValuationValue)
      : props.operation.atypicalClassicValuationValue,
    atypicalPrecariousnessValuationValue: props.operation.headOperation
      ? (props.operation.atypicalPrecariousnessValuationValue ?? props.operation.precariousnessValuationValue)
      : props.operation.atypicalPrecariousnessValuationValue,
    estimatedCommitmentDate: props.operation.estimatedCommitmentDate ?? props.operation.signedDate,
    estimatedEndOperationDate: props.operation.estimatedEndOperationDate ?? props.operation.actualEndWorksDate,
    leadingEntityId: props.operation.entity.id,
  }

  simulationApi
    .create(request)
    .then(async (response) => {
      snackbar.setSuccess(
        `${props.operation.stepId === 0 ? 'La simulation' : "L'opération"} a bien été dupliquée`,
        5000
      )

      if (props.disableUnsavedData) {
        props.disableUnsavedData()
      }
      await router.push({
        name: props.operation.stepId === 0 ? 'SimulationOneView' : 'OperationOneView',
        params: { id: response.data?.id },
        query: { duplicate: 'true' },
      })
      emit('update:operation', response.data)

      emit('update:model-value', false)
    })
    .catch(async (err) =>
      snackbar.setError(
        await handleAxiosException(err, () =>
          Promise.resolve(
            `Une erreur est survenue lors de la duplication de ${
              props.operation.stepId === 0 ? 'la simulation' : "l'opération"
            }`
          )
        )
      )
    )
}

//create regroupement
const name = ref('')
const formRef = ref<VForm | null>(null)
const operationsGroup = ref(emptyValue<OperationsGroup>())

const createOperationGroup = async () => {
  const validate = await formRef.value!.validate()
  if (validate.valid) {
    const request: OperationsGroupRequest = {
      name: name.value,
      beneficiaryId: props.operation.beneficiary!.id,
      subcontractorId: props.operation.subcontractor?.id ?? 0,
      entityId: props.operation!.entity.id,
      customerFinancialIncentive: undefined,
      commercialOfferWithoutFinancialIncentive: undefined,
      onlyEpcOperations: props.operation.epcBonusParameterValues != null,
      atypicalClassicValuationValue: null,
      atypicalPrecariousnessValuationValue: null,
    }
    handleAxiosPromise(operationsGroup, operationsGroupApi.create(request), {
      afterError: () => snackbar.setError(operationsGroup.value.error ?? 'Erreur lors de la création du regroupement'),
      afterSuccess: () => {
        snackbar.setSuccess('Regroupement créé avec succès')
        duplicateSimulation()
      },
    })
  }
}
</script>
