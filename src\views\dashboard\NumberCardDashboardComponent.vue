<template>
  <VSheet
    class="dashboard-card-number d-flex flex-column"
    :color="selected ? '#E7EEFC' : undefined"
    position="relative"
    :border="
      targetedValue !== undefined
        ? (localNumberModelValue === targetedValue ? 'success' : 'warning') + ' left s-lg opacity-100'
        : selected
          ? 'primary md opacity-100'
          : 'primary md opacity-0'
    "
    :class="{
      'opacity-50': !selected && disabled,
    }"
  >
    <div class="d-flex">
      <div class="dashboard-card-number__title">{{ title }}</div>
      <slot name="after-title">
        <!-- A laisser car pratique pour debug -->
        <!-- <NjIconBtn size="small" icon="mdi-refresh" density="compact" @click="load"></NjIconBtn> -->
        <VTooltip v-if="tooltip" max-width="320">
          <template #activator="{ props }">
            <VIcon icon="mdi-information-outline" v-bind="props" size="x-small" color="#60798B" />
          </template>
          {{ tooltip }}
        </VTooltip>
      </slot>
    </div>
    <div class="dashboard-card-number__subtitle">{{ subtitle }}</div>
    <VSpacer />
    <VAlert v-if="error" type="error">{{ error }}</VAlert>
    <slot v-else name="value" :value="localModelValue" :loading="currentLoading">
      <div class="dashboard-card-number__value-line">
        <template v-if="value !== undefined">
          <div class="dashboard-card-number__value">{{ Math.round(localNumberModelValue) }}</div>
        </template>
        <slot name="after-value"> </slot>
      </div>
    </slot>
  </VSheet>
</template>

<script setup lang="ts">
import { VAlert } from 'vuetify/components'
import { dashboardCardKey, dashboardDataKey } from './keys'
import { gsap } from 'gsap'
import type { OperationFilter } from '@/api/operation'

const loading = defineModel<boolean>('loading')

const selected = defineModel<boolean>('selected', {
  default: false,
})

const props = defineProps<{
  title: string
  subtitle?: string
  value?: number | ((filter: OperationFilter) => Promise<any>)
  tooltip?: string
  targetedValue?: number
  disabled?: boolean
}>()

const { loading: globalLoading, filter } = inject(dashboardDataKey, {
  loading: ref(false),
  filter: ref({}),
  dashboardFilter: ref({}),
})

watch(
  filter,
  () => {
    load()
  },
  {
    deep: true,
  }
)

const load = () => {
  loading.value = true
  error.value = ''
  if (typeof props.value === 'function') {
    props
      .value(filter.value)
      .then((it) => {
        if (typeof it === 'number') {
          nextTick(() => {
            gsap.to(localNumberModelValue, {
              value: it,
              duration: 0.5,
            })
          })
        } else {
          localModelValue.value = it
        }
      })
      .catch(async (e: unknown) => {
        error.value = await handleAxiosException(e)
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    nextTick(() => {
      gsap.to(localNumberModelValue, {
        value: props.value,
        duration: 1,
      })
    })
  }
}

const localNumberModelValue = ref(0)
const localModelValue = ref()
const error = ref('')

const currentLoading = computed(() => {
  return globalLoading.value || loading.value
})
watch(
  () => props.value,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

provide(dashboardCardKey, {
  selected,
  disabled: computed(() => props.disabled),
})
</script>

<style lang="scss">
.dashboard-card-number {
  padding: 8px 12px;
  background-color: white;

  &__title {
    font-size: 0.875rem;
    flex-grow: 1;
  }
  &__subtitle {
    font-size: 0.75rem;
    color: #60798b;
  }
  &__value {
    font-size: 1.5rem;
    font-weight: 700;
    flex-grow: 1;
  }

  &__value-line {
    display: flex;
    align-items: end;
  }
}
</style>
