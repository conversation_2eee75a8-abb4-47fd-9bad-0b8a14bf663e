import { useUserStore } from './user'

export const GID_STORAGE_KEY = 'gid'
export const SESSION_ID_STORAGE_KEY = 'session-id'
export const INCREMENT_ID_STORAGE_KEY = 'increment-id'
const ANALYTICS_EVENT_TABLE_NAME = 'analytics-event'

function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('capte', 1)
    request.onupgradeneeded = function (event) {
      const db = (event.target as any).result as IDBDatabase
      if (!db.objectStoreNames.contains(ANALYTICS_EVENT_TABLE_NAME)) {
        db.createObjectStore(ANALYTICS_EVENT_TABLE_NAME, { keyPath: 'localId', autoIncrement: true })
      }
    }
    request.onsuccess = () => resolve(request.result)
    request.onerror = () => reject(request.error)
  })
}

let db: IDBDatabase | undefined = undefined
function getDbConnection(): Promise<IDBDatabase> {
  if (db === undefined) {
    return openDB().then((it) => {
      db = it
      return it
    })
  } else {
    return Promise.resolve(db)
  }
}

export type AnalyticsEvent = {
  id: number
  sessionId: bigint
  userId: number
  userGid: string
  eventType: string
  timestamp: Date // ISO 8601 string, corresponds to Java Instant
  metadata?: Record<string, any> // Optional, as Map can be empty/null
  userAgent: string
  screenWidth: number
  screenHeight: number
  windowWidth: number
  windowHeight: number

  orientationType: string
  hasMultipleScreens: boolean

  devicePixelRatio: number
  deviceFontRatio: number
}

export type LocalAnalyticsEvent = AnalyticsEvent & { localId: number }

let sessionId: bigint = 0n
let userStore: ReturnType<typeof useUserStore> | undefined = undefined
export const trace = async (eventName: string, metadata?: Record<string, any>) => {
  // console.debug('trace', eventName, metadata)
  if (userStore === undefined) {
    userStore = useUserStore()
  }

  const event: Omit<LocalAnalyticsEvent, 'localId'> = {
    id: 0,
    // localId: 0,
    userGid: currentGid,
    userId: userStore.currentUser.id,
    sessionId,
    eventType: eventName,
    metadata,
    timestamp: new Date(),

    userAgent: navigator.userAgent,
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    windowWidth: screen.width,
    windowHeight: screen.height,
    hasMultipleScreens: (screen as any).isExtended,
    orientationType: screen.orientation.type,

    devicePixelRatio: window.devicePixelRatio,
    deviceFontRatio,
  }

  const db = await getDbConnection()
  const tx = db.transaction(ANALYTICS_EVENT_TABLE_NAME, 'readwrite')
  tx.objectStore(ANALYTICS_EVENT_TABLE_NAME).add(event)
  // tx.onerror = () => {
  //   console.error('error on transaction')
  // }
  // tx.oncomplete = (e) => {
  //   console.info('trace saved', e)
  // }
  // tx.onabort = () => {
  //   console.error('eeeeee')
  // }
}

export const fetchEvents = (size: number = 50): Promise<LocalAnalyticsEvent[]> =>
  new Promise(async (resolve, reject) => {
    const db = await getDbConnection()
    const tx = db.transaction(ANALYTICS_EVENT_TABLE_NAME, 'readonly')
    const store = tx.objectStore(ANALYTICS_EVENT_TABLE_NAME)
    const request = store.getAll(null, size)
    request.onsuccess = (ev: any) => {
      resolve(ev.target.result)
    }
    request.onerror = (ev: any) => {
      reject(ev)
    }
  })

export const removeEvents = (events: LocalAnalyticsEvent[]): Promise<unknown> =>
  new Promise(async (resolve, reject) => {
    const db = await getDbConnection()
    const tx = db.transaction(ANALYTICS_EVENT_TABLE_NAME, 'readwrite')
    const store = tx.objectStore(ANALYTICS_EVENT_TABLE_NAME)

    events.forEach((it) => {
      store.delete(it.localId)
    })

    tx.oncomplete = (ev: any) => {
      resolve(ev.target.result)
    }
    tx.onerror = (ev: any) => {
      reject(ev)
    }
  })

export const setSessionId = (newSessionId: bigint) => {
  sessionId = newSessionId
  sessionStorage.setItem(SESSION_ID_STORAGE_KEY, newSessionId.toString())
}
let currentGid = localStorage.getItem(GID_STORAGE_KEY) ?? ''
export const setGid = (newGid: string) => {
  currentGid = newGid
  localStorage.setItem(GID_STORAGE_KEY, newGid)
}

export const initSessionId = () => {
  if (sessionId == 0n) {
    const persistedSessionId = sessionStorage.getItem(SESSION_ID_STORAGE_KEY) ?? ''
    if (persistedSessionId.length > 0) {
      setSessionId(BigInt(persistedSessionId))
    } else {
      setSessionId(randomLong())
    }
  }
}

let deviceFontRatio = 1
export const setFontRatio = (fontRatio: number) => {
  deviceFontRatio = fontRatio
}
