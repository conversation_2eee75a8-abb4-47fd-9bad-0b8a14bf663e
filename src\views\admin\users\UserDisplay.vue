<template>
  <VRow class="flex-column flex-grow-0" dense>
    <VCol>
      <NjDisplayValue label="Utilisateur" :value="displayFullnameUser(user)" />
    </VCol>
    <VCol>
      <NjDisplayValue label="GID" :value="user.gid ?? ''" />
    </VCol>
    <VCol>
      <NjDisplayValue label="Fonction" :value="user.function" />
    </VCol>
    <VCol>
      <VRow>
        <VCol class="value__label"> Rôles </VCol>
        <VCol>
          <div v-for="role in user.roles" :key="role" class="value__value text-right">
            {{ displayProfiles.find((r) => r.value == role)?.title }}
          </div>
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <VRow>
        <VCol class="value__label"> Territoires </VCol>
        <VCol>
          <div v-for="org in user.territories" :key="org.id" class="value__value text-right">
            {{ `${org.description} (${org.zoneId})` }}
          </div>
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <VRow>
        <VCol class="value__label"> Organisations </VCol>
        <VCol>
          <div v-for="org in user.entities" :key="org.id" class="value__value text-right">
            {{ `(${org.id}) ${org.name}` }}
          </div>
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import type { UserWithEntities } from '@/types/user'
import { displayFullnameUser } from '@/types/user'
import { VCol, VRow } from 'vuetify/components'
import { displayProfiles } from '@/types/user'

defineProps({
  user: {
    type: Object as PropType<UserWithEntities>,
    required: true,
  },
})
</script>
