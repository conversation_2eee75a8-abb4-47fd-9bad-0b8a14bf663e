import type { ValuationType } from '@/types/valuation'
import { isEqual } from 'lodash'
import { defineStore } from 'pinia'

export const useValuationTypesStore = defineStore('valuationTypes', () => {
  // const valuationTypes = ref<ValuationType[]>()
  const activeValuationTypes = ref<ValuationType[]>()

  const timeoutId = ref<number>()

  const load = async () => {
    clearTimeouts()

    return await valuationTypeApi
      .getAll({ displayActive: true }, { size: 1000 })
      .then((v) => {
        v.data.content.sort((arg1, arg2) => arg1.id - arg2.id)
        if (!isEqual(v, activeValuationTypes.value)) {
          activeValuationTypes.value = v.data.content
          // activeValuationTypes.value = v.data.content.filter(it => it.)
        }

        timeoutId.value = setTimeout(load, 30 * 60 * 1000)
      })
      .catch((e) => {
        logException(e)
        timeoutId.value = setTimeout(load, ((activeValuationTypes.value?.length ?? 0 > 0) ? 30 * 60 : 5) * 1000)
      })
  }

  const clearTimeouts = () => {
    if (timeoutId.value !== undefined) {
      clearTimeout(timeoutId.value)
    }
  }

  return { activeValuationTypes, load, clearTimeouts }
})
