import type { AnalyticsEvent } from '@/stores/analytics'
import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'

class AnalyticsEventApi {
  public constructor(private axios: AxiosInstance) {}

  public saveAll(request: AnalyticsEvent[]): AxiosPromise {
    return this.axios.post(
      '/analytics_events',
      request.map((it) => ({
        ...it,
        metadata: JSON.stringify(it.metadata),
        timestamp: it.timestamp.toISOString(),
      }))
    )
  }
}

export const analyticsEventApi = new AnalyticsEventApi(axiosInstance)
