<template>
  <div>
    <CardDialog
      :model-value="modelValue"
      title="Gérer les opérations"
      fixed
      @update:model-value="emits('update:model-value', $event)"
    >
      <VRow class="h-100">
        <VCol cols="6">
          <VRow class="flex-column h-100" dense>
            <VCol class="font-weight-700 flex-grow-0"> Opérations à l'étape 80 </VCol>
            <VCol class="flex-grow-0">
              <VRow>
                <VCol>
                  <SearchInput
                    :model-value="avaibleOperationsPagination.pageFilter.value.search"
                    :loading="avaibleOperationsPagination.data.value.loading"
                    @update:model-value="avaibleOperationsUpdateSearch"
                  />
                </VCol>
                <VCol>
                  <VCheckbox
                    label="Mes opérations"
                    :model-value="avaibleOperationsPagination.pageFilter.value.myRequests"
                    @update:model-value="
                      avaibleOperationsPagination.updateFilter({
                        ...avaibleOperationsPagination.pageFilter.value,
                        myRequests: $event ? true : undefined,
                      })
                    "
                  />
                </VCol>
                <VCol>
                  <VSelect
                    label="Périodes"
                    :items="periodsStore.periods"
                    item-title="name"
                    item-value="id"
                    multiple
                    @update:model-value="
                      (event: readonly number[]) =>
                        avaibleOperationsPagination.updateFilter({
                          ...avaibleOperationsPagination.pageFilter.value,
                          periodIds: event.length > 0 ? event.concat() : undefined,
                        })
                    "
                  />
                </VCol>
              </VRow>
            </VCol>
            <VCol>
              <NjDataTable
                :headers="operationsManagmentHeaders"
                :pageable="avaibleOperationsPagination.pageable.value"
                :page="avaibleOperationsPagination.data.value.value!"
                fixed
                column-fixed
                @update:pageable="avaibleOperationsPagination.updatePageable"
              >
                <template v-if="emmyFolder" #[`item.action`]="{ item }">
                  <VBtn
                    icon="mdi-plus"
                    variant="text"
                    size="small"
                    color="primary"
                    @click="showConfirmAddOperationDialog = { active: true, operation: item }"
                  />
                </template>
              </NjDataTable>
            </VCol>
          </VRow>
        </VCol>
        <VCol cols="6">
          <VRow class="flex-column h-100" dense>
            <VCol class="flex-grow-0 w-50">
              <SearchInput
                :model-value="pageFilter.search"
                :loading="data.loading"
                @update:model-value="emmyFoldersUpdateSearch"
              />
            </VCol>
            <VCol>
              <NjDataTable
                v-model:selections="selections"
                :headers="emmyFolderHeaders"
                :pageable="pageable"
                :page="data.value!"
                fixed
                @update:pageable="updatePageable"
              >
                <template #[`item.stepId`]="{ item }">
                  <EmmyFolderStepChip :step-id="item.stepId" />
                </template>
              </NjDataTable>
            </VCol>
            <VCol v-if="emmyFolder">
              <VRow class="flex-column h-100" dense>
                <VCol class="flex-grow-0"> Opérations dans le dossier Emmy: {{ emmyFolder.name }} </VCol>
                <VCol class="flex-grow-0">
                  <VRow>
                    <VCol cols="6">
                      <SearchInput
                        :model-value="operationsPagination.pageFilter.value.search"
                        :loading="operationsPagination.data.value.loading"
                        @update:model-value="operationsUpdateSearch"
                      />
                    </VCol>
                  </VRow>
                </VCol>
                <VCol>
                  <NjDataTable
                    :headers="operationsManagmentHeaders"
                    :pageable="operationsPagination.pageable.value"
                    :page="operationsPagination.data.value.value!"
                    fixed
                    @update:pageable="operationsPagination.updatePageable"
                  >
                    <template #[`item.action`]="{ item }">
                      <VBtn
                        v-if="!item.validateImportInEmmyDateTime || userStore.isAdmin"
                        icon="mdi-minus"
                        variant="text"
                        size="small"
                        color="primary"
                        @click="removeOperationFromEmmyFolder(item.id)"
                      />
                    </template>
                  </NjDataTable>
                </VCol>
              </VRow>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
      <template #actions>
        <NjBtn variant="outlined" @click="handleCancelManageOperation">Annuler</NjBtn>
        <NjBtn @click="handleConfirm">Valider</NjBtn>
      </template>
    </CardDialog>
    <OperationForEmmyFolderSummaryDialog
      v-model:show-confirm-add-operation-dialog="showConfirmAddOperationDialog"
      :add-operation="addOperationInEmmyFolder"
    />
  </div>
</template>
<script setup lang="ts">
import type { EmmyFolderFilter } from '@/api/emmyFolder'
import type { OperationFilter } from '@/api/operation'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { EmmyFolder } from '@/types/emmyFolder'
import type { Operation } from '@/types/operation'
import { useDebouncedSearch } from '@/types/search'
import OperationForEmmyFolderSummaryDialog from './OperationForEmmyFolderSummaryDialog.vue'
import EmmyFolderStepChip from './EmmyFolderStepChip.vue'

const props = defineProps({
  modelValue: Boolean,
})

const emits = defineEmits<{
  'update:model-value': [boolean]
}>()

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

const selections = ref<EmmyFolder[]>([])

const emmyFolder = computed(() => selections.value[0])

// Available operations
const avaibleOperationsPagination = usePagination<Operation, OperationFilter>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    stepIds: [80],
    emmyFolderId: 0,
    operationStatuses: ['DOING'],
  }
)
const { updateSearch: avaibleOperationsUpdateSearch } = useDebouncedSearch(
  avaibleOperationsPagination.pageFilter,
  avaibleOperationsPagination.updateFilter,
  avaibleOperationsPagination.data
)

const { data, pageable, updatePageable, pageFilter, updateFilter } = usePagination<EmmyFolder, EmmyFolderFilter>(
  (filter, pageable) => emmyFolderApi.findAll({ ...filter, myFolders: filter.myFolders ? true : undefined }, pageable),
  {
    stepIds: [80],
  }
)
const { updateSearch: emmyFoldersUpdateSearch } = useDebouncedSearch(pageFilter, updateFilter, data)

const operationsPagination = usePagination((filter, pageable) => operationApi.findAll(filter, pageable), {
  emmyFolderId: emmyFolder.value?.id,
} as OperationFilter)
const { updateSearch: operationsUpdateSearch } = useDebouncedSearch(
  operationsPagination.pageFilter,
  operationsPagination.updateFilter,
  operationsPagination.data
)

const emmyFolderHeaders: DataTableHeader[] = [
  {
    title: 'Numéro dossier',
    value: 'emmyCode',
  },
  {
    title: 'Nom',
    value: 'name',
  },
  {
    title: 'Nb. opérations',
    value: 'operationsCount',
    sortable: false,
  },
  {
    title: 'Responsable',
    value: 'creationUser',
    formater: (value) => displayFullnameUser(value),
  },
  {
    title: 'Nb. demandés kWhc',
    value: 'na4',
    sortable: false,
  },
  {
    title: 'Mnt. dem. €',
    value: 'na5',
    sortable: false,
  },
  {
    title: 'Réf. client',
    value: 'na6',
    sortable: false,
  },
]

const operationsManagmentHeaders: DataTableHeader[] = [
  {
    title: 'Nom',
    value: 'operationName',
    width: '300px',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
    width: '64px',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
    width: '64px',
  },
  {
    title: 'Action',
    value: 'action',
    sortable: false,
    width: '30px',
  },
]

const showConfirmAddOperationDialog = ref({
  active: false,
  operation: makeEmptyOperation(),
})

const addOperationInEmmyFolder = () => {
  avaibleOperationsPagination.data.value.loading = true
  operationsPagination.data.value.loading = true
  const operationId = showConfirmAddOperationDialog.value.operation.id
  emmyFolderApi
    .addOperation(emmyFolder.value?.id, operationId)
    .then(() => {
      showConfirmAddOperationDialog.value.active = false
      avaibleOperationsPagination.reload()
      operationsPagination.updatePageable({
        ...operationsPagination.pageable.value,
        sort: ['"ids:' + operationId + '",DESC'],
      })
      if (updatedOperation.value.removedOperation.find((i) => i[1] == operationId)) {
        updatedOperation.value.removedOperation = updatedOperation.value.removedOperation.filter(
          (i) => i[1] != operationId
        )
      } else {
        updatedOperation.value.addedOperations.push([emmyFolder.value.id, operationId])
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      avaibleOperationsPagination.data.value.loading = false
      operationsPagination.data.value.loading = false
    })
}

const removeOperationFromEmmyFolder = (operationId: number) => {
  avaibleOperationsPagination.data.value.loading = true
  operationsPagination.data.value.loading = true
  emmyFolderApi
    .removeOperation(emmyFolder.value?.id, operationId)
    .then(() => {
      avaibleOperationsPagination.reload()
      operationsPagination.reload()
      if (updatedOperation.value.addedOperations.find((i) => i[1] == operationId)) {
        updatedOperation.value.addedOperations = updatedOperation.value.addedOperations.filter(
          (i) => i[1] != operationId
        )
      } else {
        updatedOperation.value.removedOperation.push([emmyFolder.value.id, operationId])
      }
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      avaibleOperationsPagination.data.value.loading = false
      operationsPagination.data.value.loading = false
    })
}

const periodsStore = usePeriodsStore()

const handleConfirm = () => {
  snackbarStore.setSuccess('Vos modifcations ont bien été enregistrées')
  updatedOperation.value = {
    addedOperations: [],
    removedOperation: [],
  }
  emits('update:model-value', false)
}

watch(emmyFolder, (v) => {
  if (v) {
    operationsPagination.updateFilter({
      ...operationsPagination.pageFilter,
      emmyFolderId: v.id,
    })
  }
})

watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      ;(updatedOperation.value = {
        addedOperations: [],
        removedOperation: [],
      }),
        avaibleOperationsPagination.reload()
      operationsPagination.reload()
      selections.value = []
    } else if (
      updatedOperation.value.addedOperations.length > 0 ||
      updatedOperation.value.removedOperation.length > 0
    ) {
      handleCancelManageOperation()
    }
  }
)
const updatedOperation = ref<{
  addedOperations: number[][] //table with pairs emmyFolderId operationId in that order
  removedOperation: number[][]
}>({
  addedOperations: [],
  removedOperation: [],
})

const handleCancelManageOperation = async () => {
  await Promise.all(
    updatedOperation.value.addedOperations.map((table: number[]) => {
      return emmyFolderApi.removeOperation(table[0], table[1])
    })
  )
  await Promise.all(
    updatedOperation.value.removedOperation.map((table: number[]) => {
      return emmyFolderApi.addOperation(table[0], table[1])
    })
  )
  emits('update:model-value', false)
}
</script>
