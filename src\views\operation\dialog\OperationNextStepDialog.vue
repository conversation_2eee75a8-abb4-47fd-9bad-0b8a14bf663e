<template>
  <div>
    <NextStepDialog
      :model-value="modelValue"
      :step-id="operation?.stepId ?? 0"
      :width="operation?.stepId == 60 ? '45%' : '40%'"
      @update:model-value="(value: boolean) => emit('update:model-value', value)"
    >
      <template v-if="operation?.stepId == 60" #alert>
        <VRow class="pl-2 pr-2 pt-2">
          <VCol>
            <VAlert type="info" variant="outlined" class="text-black border border--primary">
              Si vous rencontrez des erreurs dans les kWhc obtenus/demandés les valeurs sont à corriger directement dans
              la fiche de calcul.
            </VAlert>
          </VCol>
        </VRow>
      </template>

      <template #informations>
        <template v-if="displayFinancialIncentive">
          <VForm ref="financialIncentiveFormRef">
            <NjExpansionPanel title="Incitation financière">
              <VRow class="flex-column" dense>
                <VCol>
                  <VTextField
                    v-model="commercialOfferWithoutFinancialIncentive"
                    label="Offre commerciale avant incitation financière CEE/€ TTC"
                    suffix="€"
                    :rules="[positiveNumericRuleGenerator(), requiredRule]"
                  />
                </VCol>
                <VCol>
                  <VTextField
                    v-model="customerFinancialIncentive"
                    label="Incitation financière CEE client/€ TTC"
                    suffix="€"
                    :rules="[positiveNumericRuleGenerator(), requiredRule]"
                  />
                </VCol>
                <VCol>
                  <VInput
                    :rules="[financialIncentiveAmountNotNullRule]"
                    :model-value="commercialOfferWithFinancialIncentive"
                  >
                    <NjDisplayValue
                      label="Montant de l'offre commerciale après déduction de l'incitation financière CEE/€ TTC"
                      :value="formatPriceNumber(commercialOfferWithFinancialIncentive)"
                    />
                  </VInput>
                </VCol>
                <VCol>
                  <NjDisplayValue
                    label="Marge CEE nette TTC"
                    :value="formatPriceNumber(getNetMargin(operation!, customerFinancialIncentive))"
                  />
                </VCol>
                <NjDivider />
              </VRow>
            </NjExpansionPanel>
          </VForm>
        </template>
        <template v-if="operation && operationHasMissingInformation">
          <VForm ref="missingInformationsFormRef">
            <NjExpansionPanel v-if="!operation.beneficiary">
              <template #title>
                <div class="d-flex align-center fill-width">
                  Bénéficiaire
                  <VSpacer />
                  <NjIconBtn
                    v-if="operation.beneficiary != null"
                    icon="mdi-delete"
                    color="primary"
                    @click.stop="emit('update:operation', { ...operation, beneficiary: null })"
                  />
                  <VLink
                    size="small"
                    icon="mdi-format-list-bulleted"
                    style="font-weight: initial; font-size: initial"
                    @click.stop="
                      () => {
                        emit('update:model-value', false)
                        beneficiaryDialog = true
                      }
                    "
                    >Bénéficiaires</VLink
                  >
                </div>
              </template>
              <BeneficiaryDisplayValue
                ref="beneficiaryDisplayValueRef"
                :model-value="selectedBeneficiary"
                :rules="[requiredRule]"
                :legacy="operation.legacyBeneficiary"
              />
            </NjExpansionPanel>

            <template v-if="operationHasMissingInformation">
              <NjDivider />
              <NjExpansionPanel title="Informations manquantes">
                <VRow class="flex-column" dense>
                  <template v-if="(operation?.stepId ?? 0) >= 10">
                    <VCol
                      v-if="
                        !operation.estimatedCommitmentDate && !operation.signedDate && (operation?.stepId ?? 0) < 40
                      "
                    >
                      <NjDisplayValue label="Date d'engagement prévisionnelle">
                        <template #value>
                          <div class="w-50">
                            <NjDatePicker v-model="selectedEstimatedCommitmentDate" :rules="[requiredRule]" />
                          </div>
                        </template>
                      </NjDisplayValue>
                    </VCol>
                    <VCol v-if="!operation.estimatedEndOperationDate && (operation?.stepId ?? 0) <= 40">
                      <NjDisplayValue label="Date de fin de travaux prévisionnelle">
                        <template #value>
                          <div class="w-50">
                            <NjDatePicker v-model="selectedEstimatedEndOperation" :rules="[requiredRule]" />
                          </div>
                        </template>
                      </NjDisplayValue>
                    </VCol>
                  </template>
                  <template v-if="(operation?.stepId ?? 0) >= 40">
                    <VCol v-if="!operation.offersDispatchDate">
                      <NjDisplayValue label="Date d'envoi de l'offre client">
                        <template #value>
                          <div class="w-50">
                            <NjDatePicker
                              v-model="selectedOffersDispatchDate"
                              :rules="!operation.selfWorks && operation.stepId < 60 ? [requiredRule] : []"
                            />
                          </div>
                        </template>
                      </NjDisplayValue>
                    </VCol>
                    <VCol v-if="!operation.customerOfferNumber">
                      <NjDisplayValue label="Numéro de l'offre client">
                        <template #value>
                          <div class="w-50">
                            <VTextField
                              v-model="selectedCustomerOfferNumber"
                              :rules="!operation.selfWorks && operation.stepId < 60 ? [requiredRule] : []"
                            />
                          </div>
                        </template>
                      </NjDisplayValue>
                    </VCol>
                    <VCol v-if="!operation.finalWorksType">
                      <NjDisplayValue label="Type de Chantier">
                        <template #value>
                          <div class="w-50">
                            <VSelect v-model="selectedfinalWorksType" :items="worksTypes" :rules="[requiredRule]" />
                          </div>
                        </template>
                      </NjDisplayValue>
                    </VCol>
                  </template>
                </VRow>
              </NjExpansionPanel>
            </template>
          </VForm>
          <NjDivider v-if="operation.stepId !== 20" />
        </template>
        <template v-if="operation?.stepId === 30">
          <NjExpansionPanel title="Bénéficiaire">
            <BeneficiaryDisplayValue :model-value="operation.beneficiary" :legacy="operation.legacyBeneficiary" />
          </NjExpansionPanel>
          <NjDivider />
          <VForm ref="validateStep30FormRef">
            <WorksField
              :operation="operation"
              :final-works-type="operation.finalWorksType"
              :self-works="operation.selfWorks"
              edit
              @update:final-works-type="
                emit('update:operation', { ...operation, finalWorksType: $event as WorksType | null })
              "
              @update:self-works="
                (v) => {
                  const result = { ...operation, selfWorks: v }

                  if (v) {
                    result.customerFinancialIncentive = 0
                    result.commercialOfferWithoutFinancialIncentive = 0
                  }
                  emit('update:operation', result)
                }
              "
              @delete="(operation as any)['works' + $event] = undefined"
              @selected="selectWorks"
            />
            <NjDivider />
            <NjExpansionPanel title="Offre client">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjDisplayValue label="Numéro offre client">
                    <template #value>
                      <div class="w-50">
                        <VTextField
                          :model-value="operation.customerOfferNumber"
                          :rules="operation.selfWorks ? [] : [requiredRule]"
                          @update:model-value="emit('update:operation', { ...operation, customerOfferNumber: $event })"
                        />
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
                <VCol>
                  <NjDisplayValue label="Date d'envoi de l'offre">
                    <template #value>
                      <div class="w-50">
                        <NjDatePicker
                          label="Date d'envoi de l'offre"
                          :model-value="operation.offersDispatchDate"
                          :rules="operation.selfWorks ? [] : [requiredRule]"
                          @update:model-value="emit('update:operation', { ...operation, offersDispatchDate: $event })"
                        />
                      </div>
                    </template>
                  </NjDisplayValue>
                </VCol>
              </VRow>
            </NjExpansionPanel>
          </VForm>
        </template>
        <template v-else-if="operation?.stepId === 40">
          <VForm ref="validateStep40FormRef">
            <NjExpansionPanel title="Opération">
              <VRow class="flex-column">
                <VCol v-if="operation.standardizedOperationSheet.rgeMandatory">
                  <VAlert type="info">
                    Attention les dates de validité du RGE doivent couvrir à la fois la date d'engagement et la date de
                    fin de travaux
                  </VAlert>
                </VCol>
                <VCol>
                  <NjDatePickerDisplayValue
                    label="Date d'engagement réelle*"
                    :model-value="operation.signedDate"
                    :rules="[
                      requiredRule,
                      isEmptyOrBeforeToday,
                      isBetweenDatesRuleGenerator(operation.standardizedOperationSheet),
                    ]"
                    @update:model-value="emit('update:operation', { ...operation, signedDate: $event! })"
                  />
                  <VAlert v-show="operation.period?.id !== realPeriod?.id" type="warning"
                    >La date saisie va faire changer la période :
                    <span class="font-weight-bold" style="gap: 8px">
                      {{ operation.period?.name }} <VIcon icon="mdi-arrow-right" /> {{ realPeriod?.name }}
                    </span></VAlert
                  >
                </VCol>
                <VCol
                  v-if="
                    operation!.standardizedOperationSheet.controlOrderStartDate &&
                    !isBefore(
                      new Date(operation!.signedDate!),
                      new Date(operation!.standardizedOperationSheet.controlOrderStartDate)
                    )
                  "
                >
                  <VAlert type="info" variant="outlined">
                    À compter du
                    {{ formatHumanReadableLocalDate(operation!.standardizedOperationSheet.controlOrderStartDate) }},
                    elle sera soumise à l'arrêté contrôle
                  </VAlert>
                </VCol>
              </VRow>
            </NjExpansionPanel>
            <template v-if="operation.standardizedOperationSheet.subcontractCompulsory">
              <NjDivider class="my-2" />
              <SubcontractorInputPanel
                :model-value="true"
                :is-subcontractor-mandatory="operation.standardizedOperationSheet.subcontractCompulsory"
                :subcontractor="operation.subcontractor"
                :can-certified="canCertified"
                :rules="[requiredRule]"
                @update:subcontractor="emit('update:operation', { ...operation, subcontractor: $event })"
              />
            </template>
            <template v-if="operation.standardizedOperationSheet.rgeMandatory">
              <NjDivider class="my-2" />
              <NjExpansionPanel title="RGE">
                <VRow class="flex-column" dense>
                  <VCol>
                    <NjDatePickerDisplayValue
                      label="Date d'attribution du RGE"
                      :model-value="operation.rgeGrantedDate"
                      @update:model-value="
                        (event) => {
                          emit('update:operation', { ...operation, rgeGrantedDate: event })
                          validateStep40FormRef?.validate()
                        }
                      "
                    />
                  </VCol>
                  <VCol>
                    <NjDatePickerDisplayValue
                      label="Date de fin de validité du RGE"
                      :model-value="operation.rgeEndOfValidityDate"
                      :rules="[rgeEndOfValidityDateRule]"
                      @update:model-value="
                        (event) => {
                          emit('update:operation', { ...operation, rgeEndOfValidityDate: event })
                          validateStep40FormRef?.validate()
                        }
                      "
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </template>
          </VForm>
        </template>
        <template v-else-if="operation?.stepId === 50">
          <VForm ref="validateStep50FormRef">
            <VCol>
              <VInput
                :rules="[rgeRule]"
                :model-value="formatHumanReadableLocalDate(operation.signedDate)"
                hide-details="auto"
              >
                <NjDisplayValue label="Date d'engagement" :value="formatHumanReadableLocalDate(operation.signedDate)" />
              </VInput>
            </VCol>
            <NjDivider />

            <NjExpansionPanel v-if="rgeToUpdate" title="RGE">
              <VRow class="flex-column">
                <VCol>
                  <NjDatePickerDisplayValue
                    label="Date d'attribution du RGE"
                    :model-value="operation.rgeGrantedDate"
                    :rules="[requiredRule]"
                    @update:model-value="
                      (event) => {
                        emit('update:operation', { ...operation, rgeGrantedDate: event })
                        validateStep50FormRef?.validate()
                      }
                    "
                  />
                </VCol>
                <VCol>
                  <NjDatePickerDisplayValue
                    label="Date de fin de validité du RGE"
                    :model-value="operation.rgeEndOfValidityDate"
                    :rules="[requiredRule, rgeEndOfValidityDateRule]"
                    @update:model-value="emit('update:operation', { ...operation, rgeEndOfValidityDate: $event })"
                  />
                </VCol>
              </VRow>
            </NjExpansionPanel>
            <NjDivider v-if="rgeToUpdate" />
            <NjExpansionPanel title="Installation">
              <VCard class="mt-4">
                <VCardText class="pa-2">
                  <VRow class="flex-column" dense>
                    <VCol>
                      <NjDisplayValue label="Nom" :value="operation.finalPropertyName" />
                      <NjDisplayValue label="Adresse" :value="formatAddressable(operation.finalAddress)" />
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
              <div v-if="operation.standardizedOperationSheet.possibilitySyndicate">
                <VCard
                  v-if="operation?.coOwnerShipSyndicateName || operation?.coOwnerShipSyndicateImmatriculationNumber"
                  class="mt-4"
                >
                  <VCardText class="pa-2">
                    <template v-if="operation?.coOwnerShipSyndicateName">
                      <NjDisplayValue
                        label="Syndicat de copropriété"
                        :value="operation?.coOwnerShipSyndicateName"
                      ></NjDisplayValue>
                    </template>
                    <template v-if="operation?.coOwnerShipSyndicateImmatriculationNumber">
                      <NjDisplayValue
                        label="Numéro d'immatriculation du syndicat de copropriété"
                        :value="operation?.coOwnerShipSyndicateImmatriculationNumber"
                      ></NjDisplayValue>
                    </template>
                  </VCardText>
                </VCard>
                <template v-else>
                  <VRow class="flex-column" dense>
                    <VCol>
                      <NjSwitch
                        v-model="confirmNoSyndicate"
                        :rules="[
                          conditionRuleGenerator(
                            confirmNoSyndicate,
                            'Veuillez renseigner les informations du syndicat ou confirmer leur absence.'
                          ),
                        ]"
                        label="Confirmation de non présence du syndicat de copropriété"
                      />
                    </VCol>
                  </VRow>
                </template>
                <div class="d-flex align-center fill-width">
                  <VLink
                    size="small"
                    icon="mdi-magnify"
                    style="font-weight: initial; font-size: initial"
                    @click.stop="coOwnerShipSyndicateDialog = true"
                  >
                    Syndicats de copropriété
                  </VLink>
                  <VSpacer />
                  <NjIconBtn
                    v-if="operation?.coOwnerShipSyndicateName || operation?.coOwnerShipSyndicateImmatriculationNumber"
                    icon="mdi-delete"
                    color="primary"
                    @click.stop="deleteSyndicate"
                  />
                </div>
              </div>
            </NjExpansionPanel>
            <NjExpansionPanel title="Travaux">
              <VRow class="flex-column" dense>
                <VCol>
                  <NjDatePickerDisplayValue
                    label="Date de fin de travaux réelle"
                    :model-value="operation.actualEndWorksDate"
                    :rules="[requiredRule, rgeRule, actualEndOperationRule]"
                    @update:model-value="emit('update:operation', { ...operation, actualEndWorksDate: $event })"
                  />
                </VCol>
                <VCol v-if="needControlReportInfos(operation.standardizedOperationSheet)">
                  <NjDatePickerDisplayValue
                    label="Date d'émission du rapport de contrôle"
                    :model-value="operation.controlReportIssueDate"
                    :hint="controlReportIssueDateHint"
                    :rules="[
                      requiredRule,
                      () =>
                        !controlReportInfos ||
                        controlReportInfos.differenceFromNowInDays >= 0 ||
                        'Date limite d\'envoi du rapport dépassée',
                    ]"
                    @update:model-value="
                      emit('update:operation', { ...operation, controlReportIssueDate: $event as LocalDate })
                    "
                  />
                </VCol>
                <VCol v-if="controlReportIssueDateHint">
                  <VAlert v-if="controlReportInfos!.differenceFromNowInDays >= 0" type="info">{{
                    controlReportIssueDateHint
                  }}</VAlert>
                  <VAlert v-if="controlReportInfos!.differenceFromNowInDays < 0" type="error">
                    Date limite d'envoi du rapport dépassée (au maximum le
                    {{ formatHumanReadableLocalDate(controlReportInfos!.limitDate) }}).<br />
                    Veuillez déclarer l'opération comme étant "Non Conforme"</VAlert
                  >
                </VCol>
                <VCol v-if="operation.standardizedOperationSheet.operationCode === 'BAR-TH-107-SE'">
                  <NjDatePickerDisplayValue
                    label="Date de fin de contrat"
                    :model-value="operation.endContractDate"
                    :rules="[requiredRule, () => !showWarningEndContract || 'Durée de contract invalide']"
                    @update:model-value="emit('update:operation', { ...operation, endContractDate: $event })"
                  />
                </VCol>
                <VCol v-if="operation.standardizedOperationSheet.operationCode === 'BAR-TH-107-SE'">
                  <NjDisplayValue
                    label="Durée du contrat"
                    :value="contractDuration === 0 ? '' : contractDuration + ' ans'"
                  />
                </VCol>
                <VCol v-if="showWarningEndContract">
                  <ErrorAlert type="warning" show
                    >La durée entre la date de fin de travaux réelle et la date de fin de contrat ne correspond pas à la
                    durée du contrat saisi dans l'outil de calcul ({{ declaredContractDuration }}). Veuillez corriger
                    dans l'outil de calcul la durée résiduelle. La durée du contrat est prise en années pleines (par
                    exemple une durée de 3 ans et 8 mois ne compte que pour une durée de 3 ans).</ErrorAlert
                  >
                </VCol>
              </VRow>
            </NjExpansionPanel>
            <NjDivider />
            <WorksField
              :final-works-type="operation.finalWorksType"
              :operation="operation"
              edit
              required-works
              @update:final-works-type="updateOperationByFieldname('finalWorksType', $event)"
              @update:self-works="updateSelfWork"
              @delete="updateOperationByFieldname(('works' + $event) as any, undefined)"
              @selected="selectWorks"
            />
            <template v-if="!operationHasWorks && !isCertynergie">
              <NjDivider />
              <WorksField
                :operation="operation"
                hide-self-works
                hide-works-type
                edit
                required-works
                @delete="(operation as any)['works' + $event] = undefined"
                @selected="selectWorks"
              />
            </template>
            <NjDivider />
            <NjExpansionPanel title="Valorisation">
              <DetailValuationDisplayValue :operation="operation" simulate-step60 />
            </NjExpansionPanel>
            <template v-if="operation.standardizedOperationSheet.subcontractCompulsory">
              <NjDivider />

              <SubcontractorInputPanel
                model-value
                :subcontractor="operation.subcontractor"
                :rules="[requiredRule]"
                @update:subcontractor="emit('update:operation', { ...operation, subcontractor: $event })"
              />
            </template>
            <template
              v-if="
                isControlOrganismRequired ||
                operation.standardizedOperationSheet.controlOrderNature === 'HUNDRED_PERCENT'
              "
            >
              <NjDivider />
              <NjExpansionPanel>
                <template #title>
                  <div class="d-flex full-width align-center">
                    <div class="flex-grow-1">Organisme de contrôle</div>
                    <div class="flex-grow-1 text-end text-no-wrap">
                      <NjIconBtn
                        v-if="controlOrganism !== null"
                        icon="mdi-delete"
                        color="primary"
                        @click.stop="controlOrganism = null"
                      />
                      <VLink
                        size="small"
                        icon="mdi-format-list-bulleted"
                        style="font-weight: initial; font-size: initial"
                        @click.stop="displayControlOrganisms = true"
                        >Organismes</VLink
                      >
                    </div>
                  </div>
                </template>
                <!-- <VTextField
                  :model-value="operation.orderNumber"
                  :rules="
                    operation.standardizedOperationSheet.controlOrderNature === 'HUNDRED_PERCENT' ? [requiredRule] : []
                  "
                  label="Numéro de commande"
                  class="mb-4"
                  @update:model-value="emit('update:operation', { ...operation, orderNumber: $event })"
                /> -->
                <VInput v-model="controlOrganism" :rules="step50ControlOrganismRule">
                  <ControlOrganismDisplayValue v-model="controlOrganism" />
                </VInput>
              </NjExpansionPanel>
              <ControlOrganismDialog v-model="displayControlOrganisms" v-model:control-organism="controlOrganism" />
            </template>

            <template v-if="operation.heatFundActive">
              <NjDivider />

              <NjExpansionPanel title="Fonds chaleurs">
                <VRow class="flex-column" dense>
                  <VCol>
                    <VTextField
                      :rules="[requiredRule]"
                      label="Numéro Ademe"
                      :model-value="operation.ademeCode"
                      @update:model-value="emit('update:operation', { ...operation, ademeCode: $event })"
                    />
                  </VCol>
                  <VCol>
                    <NjDatePickerDisplayValue
                      :rules="[requiredRule]"
                      label="Date de signature de la convention Ademe"
                      :model-value="operation.ademeConventionSignedDate"
                      @update:model-value="
                        emit('update:operation', { ...operation, ademeConventionSignedDate: $event })
                      "
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </template>
          </VForm>
        </template>
        <template v-else-if="operation?.stepId === 70">
          <ErrorAlert type="info" message="Souhaitez-vous valider le contrôle de l'opération ?" variant="outlined" />
        </template>

        <DetailValuationDisplayValue
          v-if="operation?.stepId == 60"
          v-slot="slotProps"
          :operation="operation"
          simulate-step60
        >
          <NjExpansionPanel title="CEE Classique en kWhc">
            <VRow class="flex-column" dense>
              <VCol>
                <NjDisplayValue
                  label="kWhc"
                  :value="
                    'Réservés: ' +
                    formatNumber(operation.reservedClassicCumac) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(operation.classicCumac)
                  "
                />
              </VCol>
              <VCol>
                <NjDisplayValue
                  label="Valorisation €/MWhc"
                  :value="
                    'Réservés: ' +
                    formatNumber(operation.reservedClassicValuationValue ?? 0) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(slotProps.classicValuation.value ?? 0)
                  "
                />
              </VCol>
              <VCol>
                <NjDisplayValue
                  label="Montant en €"
                  :value="
                    'Réservés: ' +
                    formatNumber(
                      ((operation.reservedClassicValuationValue ?? 0) * operation.reservedClassicCumac) / 1000
                    ) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(slotProps.classicValuation.total ?? 0)
                  "
                />
              </VCol>
            </VRow>
          </NjExpansionPanel>

          <NjDivider />

          <NjExpansionPanel title="CEE Précarité en kWhc">
            <VRow class="flex-column" dense>
              <VCol>
                <NjDisplayValue
                  label="kWhc"
                  :value="
                    'Réservés: ' +
                    formatNumber(operation.reservedPrecariousnessCumac) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(operation.precariousnessCumac)
                  "
                />
              </VCol>
              <VCol>
                <NjDisplayValue
                  label="Valorisation en €/MWhc"
                  :value="
                    'Réservés: ' +
                    formatNumber(operation.reservedPrecariousnessValuationValue ?? 0) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(slotProps.precariousnessValuation.value ?? 0)
                  "
                />
              </VCol>
              <VCol>
                <NjDisplayValue
                  label="Montant en €"
                  :value="
                    'Réservés: ' +
                    formatNumber(
                      ((operation.reservedPrecariousnessValuationValue ?? 0) * operation.reservedPrecariousnessCumac) /
                        1000
                    ) +
                    ' Obtenus/Demandés: ' +
                    formatNumber(slotProps.precariousnessValuation.total)
                  "
                />
              </VCol>
            </VRow>
          </NjExpansionPanel>

          <NjDivider />
          <NjExpansionPanel title="Instructeur de l'opération">
            <VRow class="flex-column">
              <VCol>
                <RemoteAutoComplete
                  label="Instructeur"
                  :model-value="operation.instructor"
                  :query-for-one="(user: User) => userApi.getOne(user.id)"
                  :query-for-all="
                    (s, pageable) =>
                      userApi.getAll(pageable, {
                        search: s,
                        roles: ['INSTRUCTEUR'] as ProfileType[],
                        active: true,
                      })
                  "
                  :item-title="(item: User) => displayFullnameUser(item)"
                  return-object
                  :rules="[requiredRule]"
                  infinite-scroll
                  clearable
                  @update:model-value="emit('update:operation', { ...operation, instructor: $event })"
                />
              </VCol>
            </VRow>
          </NjExpansionPanel>
        </DetailValuationDisplayValue>
        <template v-if="missingDocumentTypes.length">
          <NjDivider />
          <NjExpansionPanel title="Documents requis">
            <GenericDocumentSubmit
              ref="genericDocumentSubmit"
              mode="nextStep"
              :operation-id="operation.id"
              :operations-group-id="operation.operationsGroup?.id"
              :hide-input-file="!displayInputDocument"
            />
          </NjExpansionPanel>
        </template>
      </template>
      <template v-if="operation?.stepId === 40" #more>
        <NjExpansionPanel title="Doublon/Cumul">
          <VCol>
            <VSwitch v-model="operation!.customerDuplicate" label="Recherche dans la base du client" />
          </VCol>
        </NjExpansionPanel>
      </template>
      <template #actions>
        <VCardActions>
          <VSpacer />
          <NjBtn variant="outlined" @click="emit('update:model-value', false)"> Annuler </NjBtn>
          <VTooltip :disabled="!disabledNext" :text="disabledNextReason" location="bottom">
            <template #activator="{ props }">
              <div v-bind="props">
                <NjBtn :disabled="disabledNext" @click="goToNextStep"> Enregistrer et valider l'étape </NjBtn>
              </div>
            </template>
          </VTooltip>
        </VCardActions>
      </template>
    </NextStepDialog>
    <BeneficiaryDialog
      v-model="beneficiaryDialog"
      :can-certified="canCertified"
      :selected="selectedBeneficiary ? [selectedBeneficiary] : ([] as Beneficiary[])"
      @update:selected="handleUpdateSelectedBeneficiary($event)"
      @update:model-value="emit('update:model-value', true)"
    />
    <CoOwnerShipSyndicateDialog
      v-model="coOwnerShipSyndicateDialog"
      :selected="operation.coOwnerShipSyndicate ? [operation.coOwnerShipSyndicate] : ([] as CoOwnerShipSyndicate[])"
      :operation-entity="operation.entity"
      :address="{
        street: operation.finalAddress?.street || operation.property?.streetName || '',
        postalCode: operation.finalAddress?.postalCode || operation.property?.postalCode || '',
        city: operation.finalAddress?.city || operation.property?.city || '',
        country: null,
      }"
      @update:selected="handleUpdateSelectedCoOwnerShipSyndicate"
    />
  </div>
</template>
<script lang="ts" setup>
import { userApi } from '@/api/user'
import BeneficiaryDisplayValue from '@/components/BeneficiaryDisplayValue.vue'
import ErrorAlert from '@/components/ErrorAlert.vue'
import NjDivider from '@/components/NjDivider.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import { useDialogStore } from '@/stores/dialog'
import { useUserStore } from '@/stores/user'
import type { Beneficiary } from '@/types/beneficiary'
import { type ControlOrganism, controlTypeLabel } from '@/types/controlOrganism'
import { type LocalDate, formatHumanReadableLocalDate } from '@/types/date'
import { isCertynergie } from '@/types/debug'
import { formatNumber, formatPriceNumber } from '@/types/format'
import { type Operation, getNetMargin } from '@/types/operation'
import {
  conditionRuleGenerator,
  isEmptyOrBeforeToday,
  positiveNumericRuleGenerator,
  requiredRule,
  rgeEndOfValidityDateRule as rgeEndOfValidityDateBaseRule,
  isBetweenDatesRuleGenerator,
} from '@/types/rule'
import { type ProfileType, type User, displayFullnameUser } from '@/types/user'
import { type Works, type WorksType, worksTypes } from '@/types/works'
import ControlOrganismDialog from '@/views/controlorganism/ControlOrganismDialog.vue'
import { missingDocumentTypeIdsKey } from '@/views/dashboard/keys'
import NextStepDialog from '@/views/dialog/NextStepDialog.vue'
import GenericDocumentSubmit from '@/views/document/GenericDocumentSubmit.vue'
import { useRealPeriod } from '@/views/operationComposition'
import { differenceInBusinessDays, differenceInYears, isBefore } from 'date-fns'
import { clone } from 'lodash'
import { VAlert, VTooltip } from 'vuetify/components'
import { VForm } from 'vuetify/components/VForm'
import BeneficiaryDialog from '../../dialog/BeneficiaryDialog.vue'
import DetailValuationDisplayValue from '../DetailValuationDisplayValue.vue'
import SubcontractorInputPanel from '../SubcontractorInputPanel.vue'
import WorksField from '../WorksField.vue'
import { needControlReportInfos } from '@/types/calcul/standardizedOperationSheet'
import { formatAddressable } from '@/types/address'
import { type CoOwnerShipSyndicate } from '@/types/coOwnerShipSyndicate'
import CoOwnerShipSyndicateDialog from '../../dialog/coOwnerShipSyndicateDialog.vue'

const props = defineProps({
  modelValue: Boolean,
  operation: {
    type: Object as PropType<Operation>,
    default: () => makeEmptyOperation(),
  },
})

const emit = defineEmits<{
  'update:model-value': [value: boolean]
  'update:operation': [value: Operation]
  reloadMissingDocuments: [void]
  validated: [void]
}>()

const dialogStore = useDialogStore()
const userStore = useUserStore()

const confirmNoSyndicate = ref(false)
const coOwnerShipSyndicateDialog = ref(false)

const validateStep30FormRef = useTemplateRef('validateStep30FormRef')
const validateStep40FormRef = useTemplateRef('validateStep40FormRef')
const validateStep50FormRef = useTemplateRef('validateStep50FormRef')
const financialIncentiveFormRef = useTemplateRef('financialIncentiveFormRef')
const missingInformationsFormRef = useTemplateRef('missingInformationsFormRef')

const rgeToUpdate = ref(false)

// Chantier
const selectWorks = (works: Works[]) => {
  updateOperationByPartial({
    works1: works[0],
    works2: works[1],
    works3: works[2],
  })
}

const selectedEstimatedCommitmentDate = ref<LocalDate | null>(null)
const selectedEstimatedEndOperation = ref<LocalDate | null>(null)
const selectedCustomerOfferNumber = ref<string>('')
const selectedOffersDispatchDate = ref<LocalDate | null>(null)
const selectedfinalWorksType = ref<WorksType | null>(null)
const selectedBeneficiary = ref<Beneficiary | null>()
const beneficiaryDialog = ref(false)
const handleUpdateSelectedBeneficiary = (selected: Beneficiary[]) => {
  if (selected.length) {
    if (
      props.operation.operationsGroup &&
      selectedBeneficiary.value?.id != selected[0].id &&
      !props.operation.fromBocee
    ) {
      dialogStore.addAlert2({
        message: "Impossible de changer de bénéficiaire car l'opération est dans un regroupement",
        title: 'Edition de bénéficiaire',
        maxWidth: '300px',
      })
      return
    }
    selectedBeneficiary.value = selected[0]
  } else {
    selectedBeneficiary.value = null
  }
}

const { missingDocumentTypes, reload: reloadMissingDocumentTypes } = inject(missingDocumentTypeIdsKey)!
const isDocumentMissing = () => !!missingDocumentTypes.value.length

const goToNextStep = async () => {
  const operationToSend = clone(props.operation)

  if (
    (operationToSend.stepId == 30 && !(await validateStep30FormRef.value!.validate()).valid) ||
    (operationToSend.stepId == 40 && !(await validateStep40FormRef.value!.validate()).valid) ||
    (operationToSend.stepId == 50 && !(await validateStep50FormRef.value!.validate()).valid) ||
    (displayFinancialIncentive.value && !(await financialIncentiveFormRef.value!.validate()).valid)
  ) {
    nextTick(() => {
      const el = document.querySelector('.v-input--error')
      el?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    })
    return
  }
  if (genericDocumentSubmitRef.value) {
    const sendDocumentsResponse = (await genericDocumentSubmitRef.value?.uploadFiles()) ?? false
    await reloadMissingDocumentTypes()
    if (
      sendDocumentsResponse === false ||
      sendDocumentsResponse.some((it) => it.type === 'error') ||
      missingDocumentTypes.value.length
    ) {
      return
    }
  }
  if (missingInformationsFormRef.value) {
    if ((await missingInformationsFormRef.value!.validate()).valid) {
      if (!operationToSend.beneficiary?.id) {
        operationToSend.beneficiary = selectedBeneficiary.value!
      }
      if (!operationToSend.estimatedCommitmentDate) {
        operationToSend.estimatedCommitmentDate = selectedEstimatedCommitmentDate.value!
      }
      if (!operationToSend.estimatedEndOperationDate) {
        operationToSend.estimatedEndOperationDate = selectedEstimatedEndOperation.value!
      }
      if (!operationToSend.customerOfferNumber) {
        operationToSend.customerOfferNumber = selectedCustomerOfferNumber.value!
      }
      if (!operationToSend.offersDispatchDate) {
        operationToSend.offersDispatchDate = selectedOffersDispatchDate.value!
      }
      if (!operationToSend.finalWorksType) {
        operationToSend.finalWorksType = selectedfinalWorksType.value!
      }
    } else {
      return
    }
  }

  emit('update:operation', {
    ...operationToSend,
    commercialOfferWithoutFinancialIncentive: commercialOfferWithoutFinancialIncentive.value,
    customerFinancialIncentive: customerFinancialIncentive.value,
    controlOrganism: controlOrganism.value,
  })
  emit('validated')
}

const customerFinancialIncentive = ref(0)
const commercialOfferWithoutFinancialIncentive = ref(0)

const operationHasWorks = ref(false)
watch(
  () => props.modelValue,
  (v) => {
    if (v) {
      customerFinancialIncentive.value = clone(props.operation.customerFinancialIncentive)
      commercialOfferWithoutFinancialIncentive.value = clone(props.operation.commercialOfferWithoutFinancialIncentive)
      controlOrganism.value = props.operation.controlOrganism

      rgeToUpdate.value =
        props.operation.stepId === 50 &&
        rgeRule.value(formatHumanReadableLocalDate(props.operation.signedDate)) !== true

      operationHasWorks.value = !!props.operation.works1
    }
  }
)

const displayInputDocument = computed(() => {
  if (props.operation.operationsGroup) {
    return missingDocumentTypes.value.filter((item) => !item.requiredInOperationsGroupForOperationInOperationsGroup)
      .length
  }
  return true
})

const commercialOfferWithFinancialIncentive = computed(
  () => commercialOfferWithoutFinancialIncentive.value - customerFinancialIncentive.value
)

const displayFinancialIncentive = computed(
  () =>
    (props.operation?.stepId ?? 0) >= 30 &&
    (props.operation?.stepId ?? 0) < 60 &&
    !props.operation.selfWorks &&
    (props.operation.customerFinancialIncentive == 0 ||
      props.operation.commercialOfferWithoutFinancialIncentive == 0 ||
      props.operation.customerFinancialIncentive == props.operation.commercialOfferWithoutFinancialIncentive)
)

const requirementStep30 = computed(
  () =>
    props.operation?.stepId === 30 &&
    ((!props.operation.selfWorks && (!props.operation.customerOfferNumber || !props.operation.offersDispatchDate)) ||
      !props.operation.finalWorksType)
)

const requirementStep40 = computed(
  () =>
    props.operation?.stepId === 40 &&
    (!props.operation.customerDuplicate ||
      (props.operation.standardizedOperationSheet.subcontractCompulsory && !props.operation.subcontractor) ||
      !props.operation.signedDate)
)

const isControlOrganismRequired = computed(
  () =>
    props.operation?.stepId === 50 &&
    props.operation.standardizedOperationSheet?.controlOrderNature == 'HUNDRED_PERCENT' &&
    props.operation.controlOrganism?.controlType != 'ARRETE_CONTROLE'
)

const isSubContractorRequired = computed(
  () =>
    props.operation?.stepId == 50 &&
    props.operation.standardizedOperationSheet?.subcontractCompulsory &&
    !props.operation.subcontractor
)

const disabledNext = computed(() => requirementStep30.value || requirementStep40.value || isSubContractorRequired.value)

const disabledNextReason = computed(() => {
  return isDocumentMissing()
    ? "Pour valider l'étape vous devez soumettre les document requis"
    : requirementStep30.value
      ? "Pour valider l'étape 30 vous devez remplir le numéro et la date d'envoi de l'offre client, le type de chantier"
      : requirementStep40.value
        ? "Pour valider l'étape 40 vous devez renseigner les informations manquantes"
        : isSubContractorRequired.value
          ? 'Vous devez sélectionner un sous traitant'
          : ''
})

const operationHasMissingInformation = computed(
  () =>
    props.operation &&
    ((props.operation.stepId >= 10 &&
      ((props.operation.stepId <= 30 && !props.operation.estimatedCommitmentDate && !props.operation.signedDate) ||
        (props.operation.stepId <= 50 &&
          (!props.operation.beneficiary || !props.operation.estimatedEndOperationDate)))) ||
      (props.operation.stepId >= 40 &&
        (((!props.operation.customerOfferNumber || !props.operation.offersDispatchDate) &&
          props.operation.stepId < 60) ||
          !props.operation.finalWorksType)))
)

const canCertified = computed(() => userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'))

const financialIncentiveAmountNotNullRule = (value: string) => {
  return numericRule(value) && parseInt(value) > 0
    ? true
    : "Le montant de l'offre commerciale avec incitation doit être supérieur à 0"
}

const rgeEndOfValidityDateRule = computed(() => rgeEndOfValidityDateBaseRule(props.operation!))

const { rgeRule, actualEndOperationRule } = useRgeRule(computed<Operation | undefined>(() => props.operation))

const displayControlOrganisms = ref(false)
const controlOrganism = ref<ControlOrganism | null>(null)

const { realPeriod } = useRealPeriod(computed(() => props.operation))

const step50ControlOrganismRule = computed(() => {
  return [
    requiredRule,
    () =>
      props.operation.standardizedOperationSheet.allowedControlTypes.find(
        (i) => i == controlOrganism.value?.controlType
      )
        ? true
        : `Vous devez sélectionner un organisme de contrôle avec l'un des types de contrôle suivant: ${props.operation.standardizedOperationSheet.allowedControlTypes
            .map((i) => controlTypeLabel.find((j) => i == j.value)?.label)
            .join(', ')}.`,
    () => {
      if (!props.operation.standardizedOperationSheet.requireCertifiedControlOrganism) {
        return true
      }
      return controlOrganism.value?.certified ? true : "L'organisme de contrôle doit être certifié."
    },
  ]
})

const declaredContractDuration = computed(() => {
  return props.operation.parameterValues[0]['DureeContrat']
})
const contractDuration = computed((): number => {
  if (
    !!props.operation.actualEndWorksDate &&
    !!props.operation.endContractDate &&
    props.operation.standardizedOperationSheet.operationCode === 'BAR-TH-107-SE'
  ) {
    return differenceInYears(props.operation.endContractDate!, props.operation.actualEndWorksDate!)
  }
  return 0
})
const showWarningEndContract = computed(() => {
  if (props.operation.standardizedOperationSheet.operationCode === 'BAR-TH-107-SE') {
    return props.operation.parameterValues.some((it) => {
      const value = (it['DureeContrat'] as string | undefined | null)?.match?.(/(\d+) ans?/)
      if (value) {
        const y = parseInt(value[0]!)
        return contractDuration.value !== y
      } else {
        return true
      }
    })
  }
  return false
})

const genericDocumentSubmitRef = useTemplateRef('genericDocumentSubmit')

const updateOperationByFieldname = <K extends keyof Operation>(key: K, value: Operation[K]) => {
  emit('update:operation', { ...props.operation, key: value } as Operation)
}

const updateOperationByPartial = (operation: Partial<Operation>) => {
  emit('update:operation', { ...props.operation, ...operation })
}

const updateSelfWork = (b: boolean) => {
  if (b) {
    updateOperationByPartial({
      selfWorks: b,
    })
  } else {
    updateOperationByPartial({
      selfWorks: b,
      customerFinancialIncentive: 0,
      commercialOfferWithoutFinancialIncentive: 0,
    })
  }
}

const controlReportInfos = computed(() => {
  const date = props.operation.controlReportIssueDate
  if (!date || date < MIN_WORKFLOW_CONTROL_REPORT_LOCALDATE) {
    return undefined
  }
  const limitDate = date ? addBusinessDay(date, CONTROL_REPORT_MAX_RETURN_DAYS) : undefined
  if (limitDate) {
    return {
      limitDate,
      differenceFromNowInDays: differenceInBusinessDays(limitDate, new Date()),
    }
  }
  return undefined
})
const controlReportIssueDateHint = computed(() => {
  if (controlReportInfos.value) {
    return `Date limite d'envoi : ${formatHumanReadableLocalDate(controlReportInfos.value.limitDate)} (passée cette date l’opération est Non conforme)`
  }
  return undefined
})

const handleUpdateSelectedCoOwnerShipSyndicate = (selected: CoOwnerShipSyndicate[]) => {
  const updatedOperation = { ...props.operation }

  if (selected.length > 0) {
    const syndicate = selected[0]
    updatedOperation.coOwnerShipSyndicate = syndicate
    updatedOperation.coOwnerShipSyndicateName = syndicate.usageName
    updatedOperation.coOwnerShipSyndicateImmatriculationNumber = syndicate.registrationNumber
    updatedOperation.operationName = syndicate.usageName
  } else {
    updatedOperation.coOwnerShipSyndicate = null
    updatedOperation.coOwnerShipSyndicateName = ''
    updatedOperation.coOwnerShipSyndicateImmatriculationNumber = ''
  }

  emit('update:operation', updatedOperation)
}

const deleteSyndicate = () => {
  const updatedOperation = { ...props.operation }

  updatedOperation.coOwnerShipSyndicate = null
  updatedOperation.coOwnerShipSyndicateName = ''
  updatedOperation.coOwnerShipSyndicateImmatriculationNumber = ''

  emit('update:operation', updatedOperation)
}
</script>
