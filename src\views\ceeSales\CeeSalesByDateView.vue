<template>
  <VRow>
    <VCol cols="8">
      <NjDataTable
        :pageable="pageable"
        :page="data.value!"
        :headers="headers"
        :loading="data.loading"
        :on-click-row="(item) => emit('displayDate', item.saleDate)"
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.delete`]="{ item }">
          <NjIconBtn
            icon="mdi-delete-outline"
            class="h-50 w-50"
            color="primary"
            @click.stop="() => deleteSales(item.saleDate)"
          />
        </template>
      </NjDataTable>
    </VCol>
    <VCol cols="4">
      <VRow class="flex-column h-100">
        <VCol class="flex-grow-0 pb-0">
          <b>kWhc restants en stock :</b>
          <br />
          <VRow>
            <VCol>
              <NjDisplayValue label="Classique :" :value="formatNumber(totalStock?.availableClassicCumac ?? 0)" />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Précarité :"
                :value="formatNumber(totalStock?.availablePrecariousnessCumac ?? 0)"
              />
            </VCol>
          </VRow>
        </VCol>
        <VCol class="flex-grow-0 py-1">
          <b>kWhc disponibles à la vente :</b>
          <br />
          <VRow>
            <VCol>
              <NjDisplayValue
                label="Classique :"
                :value="formatNumber(totalSalableCumac?.availableClassicCumac ?? 0)"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Précarité :"
                :value="formatNumber(totalSalableCumac?.availablePrecariousnessCumac ?? 0)"
              />
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <NjDataTable
            :pageable="availablilityPageable"
            :page="availableStock.value!"
            :headers="availabilityHeaders"
            :loading="availableStock.loading"
            :disabled-row="(stock) => !stock.entity.entityDetails.canSellCumacs"
            fixed
            @update:pageable="updateAvailable"
          />
        </VCol>
      </VRow>
    </VCol>
    <AlertDialog v-bind="deleteSaleDialog.props" title="Supprimer la vente" max-width="640px">
      Êtes-vous sûr de vouloir supprimer les ventes à cette date?
    </AlertDialog>
  </VRow>
</template>
<script lang="ts" setup>
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { useSnackbarStore } from '@/stores/snackbar'
import type { CeeSaleDateDto } from '@/types/ceeSale'
import type { AvailableStock } from '@/types/ceeStock'
import type { LocalDate } from '@/types/date'
import { formatNumber } from '@/types/format'

const emit = defineEmits<{
  displayDate: [date: LocalDate]
  reload: [void]
}>()

const snackbarStore = useSnackbarStore()

const { data, pageable, updatePageable, reload } = usePagination<CeeSaleDateDto>(
  (filter, pageable) => ceeSaleApi.findAllByDate(pageable, filter),
  { active: true },
  { sort: ['saleDate,ASC'] }
)

const {
  data: availableStock,
  pageable: availablilityPageable,
  updatePageable: updateAvailable,
  reload: reloadAvailability,
} = usePagination<AvailableStock>(
  (filter, pageable) => stockCEEApi.findAllAvailable(pageable, filter),
  {},
  { sort: ['navFullId'] }
)

const headers: DataTableHeader<CeeSaleDateDto>[] = [
  {
    title: '',
    value: 'delete',
    sortable: false,
  },
  {
    title: 'Date vente',
    value: 'saleDate',
    formater: (_, value) => formatHumanReadableLocalDate(value),
  },
  {
    title: 'Nb. lignes',
    value: 'salesNumber',
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc vendus Class.',
    value: 'soldClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Mnt vendus Class.',
    value: 'classicCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc vendus Préc.',
    value: 'soldPrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Mnt vendus Préc.',
    value: 'precariousnessCumacSoldAmount',
    formater: (_, value) => formatPriceNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc total vendus',
    value: 'soldCumac',
    formater: (item) => formatNumber(item.soldClassicCumac + item.soldPrecariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Mnt total vendus',
    value: 'cumacSoldAmount',
    formater: (item) => formatPriceNumber(item.classicCumacSoldAmount + item.precariousnessCumacSoldAmount),
    cellClass: 'text-right',
    sortable: false,
  },
]

const availabilityHeaders: DataTableHeader<AvailableStock>[] = [
  {
    title: 'Organisation',
    value: 'name',
    formater: (item) => item.entity.name,
  },
  {
    title: 'kWhc disp. Class.',
    value: 'availableClassicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'kWhc disp. Préca.',
    value: 'availablePrecariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
    sortable: false,
  },
]

const totalStock = ref<AvailableStock>()
const totalSalableCumac = ref<AvailableStock>()

const deleteSaleDialog = useConfirmAlertDialog()
const deleteSales = async (date: LocalDate) => {
  if (await deleteSaleDialog.confirm()) {
    ceeSaleApi
      .deleteBySaleDate(date)
      .then(() => {
        snackbarStore.setSuccess('Les ventes ont bien été supprimées')
        reload()
        emit('reload')
      })
      .catch(async (err) =>
        snackbarStore.setError(
          await handleAxiosException(err, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression des ventes',
          })
        )
      )
  }
}

const fetchTotalStock = () =>
  stockCEEApi
    .findAllAvailable({}, { search: 'EES SA' })
    .then((response) => (totalStock.value = response.data.content[0]))

const fetchTotalAvailable = () =>
  stockCEEApi
    .findAllAvailable({}, { search: 'EES SA', eligibleForSale: true })
    .then((response) => (totalSalableCumac.value = response.data.content[0]))

const reloadTables = () => {
  reload()
  reloadAvailability()
  fetchTotalStock()
  fetchTotalAvailable()
}

onMounted(() => {
  fetchTotalStock()
  fetchTotalAvailable()
})

defineExpose({
  reloadTables,
})
</script>
