import type { AxiosInstance, AxiosPromise } from 'axios'
import axiosInstance from '.'
import type { ValuationType, ValuationTypeFilter } from '@/types/valuation'
import type { Page, Pageable } from '@/types/pagination'

const valuationTypeUrl = '/valuation_types'
class ValuationTypeApi {
  public constructor(private axios: AxiosInstance) {}

  public create(valuationType: ValuationType): AxiosPromise<ValuationType> {
    return this.axios.post(valuationTypeUrl, valuationType)
  }

  public update(id: number, valuationType: ValuationType): AxiosPromise<ValuationType> {
    return this.axios.put(`${valuationTypeUrl}/${id}`, valuationType)
  }

  public updateOrders(orderedIds: number[]): AxiosPromise<ValuationType> {
    return this.axios.put(`${valuationTypeUrl}/order`, { orderedIds })
  }

  public getAll(filter: ValuationTypeFilter, pageable: Pageable = { size: 50 }): AxiosPromise<Page<ValuationType>> {
    return this.axios.get(valuationTypeUrl, {
      params: { ...filter, ...pageable },
    })
  }
}

export const valuationTypeApi = new ValuationTypeApi(axiosInstance)
