import type { OperationFilter } from '@/api/operation'
import { omit } from 'lodash'
import type { MinimumControlRate } from './calcul/standardizedOperationSheet'
import type { LocalDate, LocalDateTime } from './date'
import type { ControlOrganism } from './controlOrganism'
import type { User } from './user'

export interface ControlOrderBatch {
  id: number
  batchCode: string
  step?: ControlOrderBatchStep
  siteComplianceRate?: number
  contactComplianceRate?: number
  controlOrganism?: ControlOrganism
  notSatisfyingRate?: number
  usedMinimumSatisfyingControlRate: MinimumControlRate
  controlOrganismReturnDate?: LocalDate
  creationDateTime: LocalDateTime
  creationUser: User
}
export interface ControlOrderBatchDto {
  controlOrderBatch: ControlOrderBatch
  standardizedOperationSheetCodes: string[]
  classicCumac: number
  precariousnessCumac: number
}

export interface ControlOrderBatchRequest
  extends Omit<
    ControlOrderBatch,
    'id' | 'batchCode' | 'controlOrganism' | 'usedMinimumSatisfyingControlRate' | 'creationDateTime' | 'creationUser'
  > {
  filter: OperationFilter
  controlOrganismId?: number
}

export const mapToControlOrderBatchRequest = (batch: ControlOrderBatch): ControlOrderBatchRequest => {
  return {
    filter: {},
    ...omit(batch, 'id', 'batchCode', 'controlOrganism'),
    controlOrganismId: batch.controlOrganism?.id,
  }
}

export interface ControlOrderBatchSummaryBeforeCreation {
  cumacClassicSum: number
  cumacPrecariousnessSum: number
  standardizedOperationSheetNumber: number
  controlOrderExportTemplateNumber: number
}

export type ControlOrderBatchStep = 'CONTROLLED_BY_CONTROL_OFFICE' | 'SENT_BY_CONTROL_OFFICE' | 'TO_BE_CONTROLLED'
export const controlOrderBatchStepItems: {
  title: string
  value: ControlOrderBatchStep | null
  shortName: string
}[] = [
  {
    title: '70',
    shortName: '70',
    value: null,
  },
  {
    title: '70A - En contrôle par le BC',
    shortName: '70A',
    value: 'CONTROLLED_BY_CONTROL_OFFICE',
  },
  {
    title: '70B - Livrables transmis par le BC',
    shortName: '70B',
    value: 'SENT_BY_CONTROL_OFFICE',
  },
  {
    title: '75 - Livrables à contrôler',
    shortName: '75',
    value: 'TO_BE_CONTROLLED',
  },
]

export const mapControlOrderStep = (status: ControlOrderBatchStep | null) =>
  controlOrderBatchStepItems.find((item) => item.value === status)?.title ?? ''

export type ControlOrderAfterSalesServiceStatus =
  | 'TO_BE_SENT_BY_CONTROL_OFFICE'
  | 'SENT_BY_CONTROL_OFFICE'
  | 'SENT_AFTER_SALES_SERVICE_SHEETS'
  | 'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS'
export const controlOrderAfterSalesServiceStatusItems: {
  title: string
  value: ControlOrderAfterSalesServiceStatus
}[] = [
  {
    title: '75A - SAV à envoyer',
    value: 'TO_BE_SENT_BY_CONTROL_OFFICE',
  },
  {
    title: '75B - SAV transmis',
    value: 'SENT_BY_CONTROL_OFFICE',
  },
  {
    title: '75C - Envoi fiches SAV et justificatifs',

    value: 'SENT_AFTER_SALES_SERVICE_SHEETS',
  },
  {
    title: '75D - Contrôle administratif fiches SAV et justificatifs',
    value: 'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS',
  },
]

export const mapControlOrderAssStatus = (status: ControlOrderAfterSalesServiceStatus | null) =>
  controlOrderAfterSalesServiceStatusItems.find((item) => item.value === status)?.title ?? ''

export type ControlOrderStatus = 'SATISFYING' | 'NOT_SATISFYING' | 'NOT_VERIFIABLE'
export const controlOrderContactStatusItems: { title: string; value: ControlOrderStatus }[] = [
  {
    title: 'Satisfaisant',
    value: 'SATISFYING',
  },
  {
    title: 'Non Satisfaisant',
    value: 'NOT_SATISFYING',
  },
]

export const controlOrderSiteStatusItems: { title: string; value: ControlOrderStatus }[] =
  controlOrderContactStatusItems.concat([
    {
      title: 'Non vérifiable',
      value: 'NOT_VERIFIABLE',
    },
  ])

export const mapControlOrderStatus = (status: ControlOrderStatus | null) =>
  controlOrderSiteStatusItems.find((item) => item.value === status)?.title ?? ''

export const CONTROL_REPORT_MAX_RETURN_DAYS = 20
export const MIN_WORKFLOW_CONTROL_REPORT_LOCALDATE = '2025-06-01'
export const MIN_WORKFLOW_CONTROL_REPORT_DATE = new Date('2025-06-01')
