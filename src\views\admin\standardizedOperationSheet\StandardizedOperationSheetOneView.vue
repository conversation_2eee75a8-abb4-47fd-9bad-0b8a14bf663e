<template>
  <NjPage
    v-bind="$attrs"
    :title="$props.id ? 'Fiche ' + standardizedOperationSheet.value?.operationCode : 'Création de Fiche de Calcul'"
    :loading="standardizedOperationSheet.loading"
    :error-message="standardizedOperationSheet.error || saving.error || globalError"
    :can-go-back="{ name: 'StandardizedOperationSheetAllView' }"
  >
    <template #after-title>
      <VIcon v-show="certified" color="#28B750" icon="mdi-shield-check-outline" />
      <VTooltip v-if="hasOperations">
        <template #activator="{ props }">
          <VBtn icon="mdi-alert" variant="text" v-bind="props"></VBtn>
        </template>
        Cette Fiche Opération Standardisé a déjà servi des simulations et opérations
      </VTooltip>
    </template>

    <template #header-actions>
      <VRow class="flex-nowrap">
        <VCol>
          <NjBtn variant="outlined" color="error"> Annuler </NjBtn>
        </VCol>
        <VCol>
          <NjBtn variant="outlined" @click="check(duplicate)"> Dupliquer </NjBtn>
        </VCol>
        <VCol>
          <NjBtn @click="save"> Enregistrer </NjBtn>
        </VCol>
      </VRow>
    </template>

    <template #body>
      <VCard>
        <VForm ref="formRef" :readonly="standardizedOperationSheet.loading">
          <VRow class="flex-column" no-gutters>
            <VCol>
              <NjExpansionPanel title="Détail Fiche Opération">
                <VRow>
                  <VCol cols="12" md="6">
                    <VTextField
                      v-model="standardizedOperationSheet.value!.operationCode"
                      label="Numéro Opération"
                      :rules="[requiredRule]"
                      :disabled="hasOperations"
                    ></VTextField>
                  </VCol>
                  <VCol cols="12" md="6">
                    <VTextField
                      v-model="standardizedOperationSheet.value!.description"
                      label="Libellé"
                      :rules="[requiredRule]"
                    ></VTextField>
                  </VCol>
                  <VCol cols="12" md="6">
                    <VFileInput v-model="pdfDocumentFile" label="Document légal de la Fiche Opération">
                      <template #append>
                        <VBtn
                          target="_blank"
                          append-icon="mdi-open-in-new"
                          variant="flat"
                          :text="standardizedOperationSheet.value?.pdfDocument?.originalFilename"
                          :disabled="!standardizedOperationSheet.value?.pdfDocument"
                          @click="downloadPdfDocument(standardizedOperationSheet.value!)"
                        />
                      </template>
                    </VFileInput>
                  </VCol>
                  <VCol cols="12" md="6">
                    <VFileInput v-model="internalPdfDocumentFile" label="Document interne de la Fiche Opération">
                      <template #append>
                        <VBtn
                          target="_blank"
                          append-icon="mdi-open-in-new"
                          variant="flat"
                          :text="standardizedOperationSheet.value?.internalPdfDocument?.originalFilename"
                          :disabled="!standardizedOperationSheet.value?.internalPdfDocument"
                          @click="downloadInternalPdfDocument(standardizedOperationSheet.value!)"
                        />
                      </template>
                    </VFileInput>
                  </VCol>
                  <VCol cols="12" md="6">
                    <NjDatePicker v-model="standardizedOperationSheet.value!.startDate" label="Date Début validité" />
                  </VCol>

                  <VCol cols="12" md="6">
                    <NjDatePicker
                      v-model="standardizedOperationSheet.value!.expirationDate"
                      label="Date Fin validité"
                    />
                  </VCol>
                  <VCol>
                    <VTextField
                      v-model="standardizedOperationSheet.value!.conventionalLifespanInYears"
                      label="Durée de vie conventionnelle en année"
                      :rules="[requiredRule, positiveOrNullNumericRuleGenerator()]"
                    />
                  </VCol>
                  <VCol cols="12">
                    <RichTextInput
                      v-model="standardizedOperationSheet.value!.warningText"
                      label="Message d'avertissement"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.certified"
                      ripple
                      label="Certifié"
                      :disabled="certified"
                      @update:model-value="
                        (v) => {
                          if (!v) {
                            standardizedOperationSheet.value!.visible = false
                          }
                        }
                      "
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.visible"
                      ripple
                      label="Visible"
                      :disabled="!standardizedOperationSheet.value!.certified"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.epcBonusEligible"
                      ripple
                      label="Éligible CPE"
                      :disabled="hasOperations && standardizedOperationSheet.value!.epcBonusEligible"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.precariousnessBonusEnabled"
                      ripple
                      label="Précarité disponible"
                      :disabled="hasOperations && standardizedOperationSheet.value!.precariousnessBonusEnabled"
                    />
                  </VCol>

                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.multipleOperation"
                      ripple
                      label="Multi Opération"
                      :disabled="hasOperations && standardizedOperationSheet.value!.multipleOperation"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.subcontractCompulsory"
                      ripple
                      label="Sous Traitance"
                      :disabled="hasOperations && !standardizedOperationSheet.value!.subcontractCompulsory"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.rgeMandatory"
                      ripple
                      label="RGE"
                      :disabled="hasOperations && !standardizedOperationSheet.value!.rgeMandatory"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <VSwitch
                      v-model="standardizedOperationSheet.value!.heatFund"
                      ripple
                      label="Fonds chaleur"
                      :disabled="hasOperations && standardizedOperationSheet.value!.heatFund"
                    />
                  </VCol>
                  <VCol cols="12" md="6">
                    <RemoteAutoComplete
                      v-model="standardizedOperationSheet.value!.substituteStandardizedOperationSheet"
                      ripple
                      label="Fiche de remplacement"
                      :query-for-all="getAllStandardizedOperationSheet"
                      :query-for-one="(item) => getStandardizedOperationSheet(item.id)"
                      item-title="operationCode"
                      return-object
                      clearable
                      infinite-scroll
                    >
                      <template #selection="{ item }">
                        <StandardizedOperationSheetChip
                          :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                        />
                      </template>
                      <template #item="{ item, props }">
                        <StandardizedOperationSheetItem
                          v-bind="props"
                          :standardized-operation-sheet="item.raw as StandardizedOperationSheet"
                        />
                      </template>
                    </RemoteAutoComplete>
                  </VCol>
                  <VCol v-if="standardizedOperationSheet.value?.operationCode.startsWith('IND-')" cols="12" md="6">
                    <VSelect
                      v-model="standardizedOperationSheet.value!.usageBonusIntSheetType"
                      label="Utilisation bonus pour IND"
                      :items="usageBonusIntSheetTypes"
                      clearable
                    ></VSelect>
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
            <VDivider />
            <VCol>
              <NjExpansionPanel title="Arreté contrôle">
                <VRow>
                  <VCol cols="4" md="4">
                    <NjDatePicker
                      v-model="standardizedOperationSheet.value!.controlOrderStartDate"
                      :disabled="hasOperations && !standardizedOperationSheet.value!.controlOrderStartDate"
                      label="Date de prise d'effet de l'arreté controle"
                      :rules="isControlOrderRuleActive ? [requiredRule] : []"
                    />
                  </VCol>
                  <VCol cols="4" md="4">
                    <VSelect
                      v-model="standardizedOperationSheet.value!.controlOrderNature"
                      :disabled="hasOperations && !standardizedOperationSheet.value!.controlOrderStartDate"
                      label="Nature du contrôle"
                      :items="controlOrderNatureLabel"
                      item-value="value"
                      item-title="label"
                      clearable
                      :rules="isControlOrderRuleActive ? [requiredRule] : []"
                    />
                  </VCol>
                  <VCol cols="4" md="4">
                    <VSelect
                      v-model="standardizedOperationSheet.value!.controlOrderType"
                      :disabled="
                        (hasOperations && !standardizedOperationSheet.value!.controlOrderStartDate) ||
                        standardizedOperationSheet.value?.controlOrderNature == 'HUNDRED_PERCENT'
                      "
                      label="Type d'arreté controle"
                      :items="controlOrderTypeLabel"
                      :rules="isControlOrderRuleActiveAndSampleNature ? [requiredRule] : []"
                      item-title="label"
                      item-value="value"
                      clearable
                      @update:model-value="clearUnusedRate"
                    />
                  </VCol>
                  <VCol cols="12" md="4">
                    <RemoteAutoComplete
                      v-model="standardizedOperationSheet.value!.controlOrderExportTemplate"
                      label="Modèle arrêté controle"
                      :query-for-all="(search: string) => controlOrderExportTemplateApi.findAll({}, { search: search })"
                      :query-for-one="(item) => controlOrderExportTemplateApi.findOne(item.id)"
                      item-title="name"
                      return-object
                      clearable
                      :rules="isControlOrderRuleActiveAndSampleNature ? [requiredRule] : []"
                    />
                  </VCol>
                  <VCol cols="12" md="4">
                    <VSelect
                      v-model="standardizedOperationSheet.value!.allowedControlTypes"
                      label="Type d'organisme de contrôle"
                      :items="controlTypeLabel"
                      item-value="value"
                      item-title="label"
                      multiple
                    />
                  </VCol>
                  <VCol cols="12" md="4">
                    <NjSwitch
                      v-model="standardizedOperationSheet.value!.requireCertifiedControlOrganism"
                      label="L'organisme de contrôle doit être certifié"
                    />
                  </VCol>
                  <VCol>
                    <NjExpansionPanel title="Taux minimal de contrôles satisfaisants réglementaire">
                      <template #title-actions>
                        <NjBtn
                          variant="outlined"
                          color="primary"
                          :disabled="standardizedOperationSheet.value?.controlOrderNature == 'HUNDRED_PERCENT'"
                          @click.stop="addMinimumSatisfyingControlRate"
                        >
                          Ajouter un taux
                        </NjBtn>
                      </template>
                      <VRow v-if="standardizedOperationSheet.value!.minimumSatisfyingControlRates?.length">
                        <VCol>
                          <VRow
                            v-for="(minimumSatisfyingControlRate, index) in standardizedOperationSheet.value!
                              .minimumSatisfyingControlRates"
                            :key="index"
                          >
                            <VCol>
                              <NjDatePicker
                                v-model="minimumSatisfyingControlRate.startDate"
                                :rules="[requiredRule]"
                                label="Date de début"
                              />
                            </VCol>
                            <VCol>
                              <NjDatePicker
                                v-model="minimumSatisfyingControlRate.endDate"
                                :rules="
                                  index == standardizedOperationSheet.value!.minimumSatisfyingControlRates.length - 1
                                    ? []
                                    : [requiredRule, isEmptyOrAfter(minimumSatisfyingControlRate.startDate)]
                                "
                                label="Date de fin"
                              />
                            </VCol>
                            <VCol
                              v-if="
                                ['SITE', 'SITE_AND_CONTACT'].includes(
                                  standardizedOperationSheet.value?.controlOrderType ?? ''
                                )
                              "
                            >
                              <VTextField
                                v-model="minimumSatisfyingControlRate.siteRate"
                                label="Taux contrôle site"
                                :rules="[requiredRule]"
                              />
                            </VCol>
                            <VCol
                              v-if="
                                ['CONTACT', 'SITE_AND_CONTACT'].includes(
                                  standardizedOperationSheet.value?.controlOrderType ?? ''
                                )
                              "
                            >
                              <VTextField
                                v-model="minimumSatisfyingControlRate.contactRate"
                                label="Taux contrôle contact"
                                :rules="[requiredRule]"
                              />
                            </VCol>
                            <VCol class="flex-grow-0">
                              <NjIconBtn
                                icon="mdi-delete"
                                color="primary"
                                @click="deleteMinimumSatisfyingControlRate(index)"
                              />
                            </VCol>
                          </VRow>
                        </VCol>
                      </VRow>
                      <span v-else>Aucun taux saisie</span>
                    </NjExpansionPanel>
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
            <VDivider />
            <VCol>
              <NjExpansionPanel title="Paramètres">
                <template #default>
                  <VRow>
                    <VCol ref="parameterListRef" cols="12">
                      <!-- eslint-disable-next-line vue/valid-v-for -->
                      <VRow v-for="(arg, i) in standardizedOperationSheet.value!.parameters" :key="arg.localId" dense>
                        <VCol>
                          <VCard>
                            <VCardText class="d-flex align-center">
                              <VIcon v-if="!hasOperations" class="sortable-handle me-2">mdi-drag</VIcon>
                              <ParameterCalculEditor
                                v-model="standardizedOperationSheet.value!.parameters[i]"
                                :multiple-operation="standardizedOperationSheet.value!.multipleOperation"
                                :disabled="hasOperations"
                                :parameters="standardizedOperationSheet.value!.parameters"
                                :mapping-tables="standardizedOperationSheet.value!.mappingTables"
                                @delete="deleteParameters(i)"
                              />
                            </VCardText>
                          </VCard>
                        </VCol>
                      </VRow>
                    </VCol>
                    <VCol class="d-flex justify-end">
                      <NjBtn v-if="!hasOperations" variant="outlined" color="primary" @click.stop="addParameters">
                        Ajouter un paramètre
                      </NjBtn>
                    </VCol>
                  </VRow>
                </template>
              </NjExpansionPanel>
            </VCol>

            <VDivider />

            <VCol>
              <NjExpansionPanel title="Tables de correspondances">
                <template #default>
                  <VRow class="flex-column">
                    <!-- eslint-disable-next-line vue/valid-v-for -->
                    <VCol v-for="(mappingTable, iMappingTables) in standardizedOperationSheet.value!.mappingTables">
                      <VRow class="flex-column">
                        <VCol v-if="iMappingTables > 0">
                          <VDivider thickness="4" color="black" />
                        </VCol>
                        <VCol>
                          <MappingTableEditor
                            v-model="standardizedOperationSheet.value!.mappingTables[iMappingTables]"
                            :parameters="fileteredParametersForMappingTable"
                            :i-mapping-tables="iMappingTables"
                            :disabled="hasOperations"
                            @delete="deleteMappingTable(iMappingTables)"
                          />
                        </VCol>
                      </VRow>
                    </VCol>
                    <VCol class="d-flex justify-end">
                      <NjBtn
                        v-if="!hasOperations"
                        variant="outlined"
                        color="primary"
                        @click.stop="ajoutTableCorrespondance"
                      >
                        Ajouter une table de correspondance
                      </NjBtn>
                    </VCol>
                  </VRow>
                </template>
              </NjExpansionPanel>
            </VCol>
            <VDivider />
            <VCol>
              <NjExpansionPanel title="Formule de calcul">
                <VRow>
                  <VCol>
                    <FormulaInput
                      v-model="standardizedOperationSheet.value!.formula"
                      label="Formule"
                      :error-messages="evaluationFormula.error"
                      :rules="[requiredRule]"
                      :disabled="hasOperations"
                      :parameters="standardizedOperationSheet.value!.parameters"
                      :mapping-tables="standardizedOperationSheet.value!.mappingTables"
                      @update:model-value="onFormulaUpdate"
                    />
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
          </VRow>
        </VForm>

        <VRow class="d-flex mt-0" no-gutters>
          <VDivider />
          <VCol cols="12">
            <NjExpansionPanel title="Aperçu">
              <template #default>
                <VTextField
                  v-model="testPostalCode"
                  label="Code postal"
                  :rules="codePostalRules"
                  class="mb-4 w-50 pr-3"
                />
                <StandardizedOperationSheetCalculator
                  v-model="paramPreview"
                  mode="preview"
                  :predefined-values="testPredefinedValues"
                  :standardized-operation-sheet="standardizedOperationSheet.value!"
                />
                <div class="d-flex mt-4" style="gap: 16px">
                  <NjBtn @click="testExportEmmy">Emmy Export Test</NjBtn>
                  <!-- <div v-if="standardizedOperationSheet.value?.id === 0"><VIcon icon="mdi-alert"></VIcon> Vous devez
                    sauvegarder votre fiche avant de tester l'export emmy</div> -->

                  <RemoteAutoComplete
                    v-model="operationToTest"
                    label="Opération de Base pour tester"
                    :query-for-one="(a) => operationApi.findById(a)"
                    :query-for-all="(v, pageable) => operationApi.findAll({ search: v }, pageable)"
                    :item-title="(arg) => `${arg.chronoCode} - ${arg.operationName}`"
                    item-value="id"
                    infinite-scroll
                    return-object
                    clearable
                  />
                </div>
              </template>
            </NjExpansionPanel>
          </VCol>
        </VRow>
        <!-- </VCardText> -->
      </VCard>
    </template>
  </NjPage>
  <ConfirmUnsavedDataDialog v-model:unsaved-data-dialog="unsavedDataDialog" @save="save" />
</template>

<script lang="ts" setup>
import { standardizedOperationSheetApi } from '@/api/standardizedOperationSheet'
import ConfirmUnsavedDataDialog from '@/components/ConfirmUnsavedDataDialog.vue'
import StandardizedOperationSheetCalculator from '@/components/StandardizedOperationSheetCalculator.vue'
import FormulaInput from '@/components/FormulaInput.vue'
import MappingTableEditor from '@/components/MappingTableEditor.vue'
import NjBtn from '@/components/NjBtn.vue'
import NjDatePicker from '@/components/NjDatePicker.vue'
import NjExpansionPanel from '@/components/NjExpansionPanel.vue'
import NjPage from '@/components/NjPage.vue'
import ParameterCalculEditor from '@/components/ParameterCalculEditor.vue'
import router from '@/router'
import { useSnackbarStore } from '@/stores/snackbar'
import {
  controlOrderNatureLabel,
  controlOrderTypeLabel,
  makeEmptyStandardizedOperationSheet,
  mapToStandardizedOperationSheetRequest,
  type StandardizedOperationSheet,
  makeEmptyMinimumControlRate,
  usageBonusIntSheetTypes,
  type ControlOrderType,
  type MinimumControlRate,
} from '@/types/calcul/standardizedOperationSheet'
import { evaluateFormula } from '@/types/calcul/formula'
import { generateMappingTableCombinaisons } from '@/types/calcul/mappingTable'
import {
  makeEmptyParameterFormula,
  parameterIdAutoIncrement,
  predefinedParameters,
} from '@/types/calcul/parameterFormula'
import type { PromisableValue } from '@/types/promisableValue'
import { isEmptyOrAfter, requiredRule, type ValidationRule, positiveOrNullNumericRuleGenerator } from '@/types/rule'
import { moveArrayElement, useSortable } from '@vueuse/integrations/useSortable'
import Formula from 'fparser'
import { cloneDeep, debounce, isEqual, uniq } from 'lodash'
import type { VForm } from 'vuetify/components/VForm'
import useAdminSidebar from '../../adminSidebar'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import StandardizedOperationSheetItem from '@/components/StandardizedOperationSheetItem.vue'
import StandardizedOperationSheetChip from '@/components/StandardizedOperationSheetChip.vue'
import RichTextInput from '@/components/RichTextInput.vue'
import type { Page, Pageable } from '@/types/pagination'
import { type Operation, downloadInternalPdfDocument, downloadPdfDocument } from '@/types/operation'
import { VCard, VCardText, VCol, VDivider, VIcon, VRow, VSelect, VSwitch, VTextField } from 'vuetify/components'
import { controlOrderExportTemplateApi } from '@/api/controlOrderExportTemplateApi'
import { operationApi } from '@/api/operation'
import type { Document } from '@/types/document'
import { controlTypeLabel } from '@/types/controlOrganism'

const props = defineProps({
  id: {
    type: Number,
    default: undefined,
  },
})

const snackbarStore = useSnackbarStore()

// Sidebar
useAdminSidebar()

// Data
const standardizedOperationSheet = ref<PromisableValue<StandardizedOperationSheet>>(
  succeedValue(makeEmptyStandardizedOperationSheet())
)

const { unsavedDataDialog, failedSave, succeedSave, check } = useUnsavedData(standardizedOperationSheet)

// Paramètres
function addParameters() {
  standardizedOperationSheet.value.value!.parameters.push(makeEmptyParameterFormula())
}

function deleteParameters(index: number) {
  standardizedOperationSheet.value.value!.parameters.splice(index, 1)
}

// Table Correspondance
function ajoutTableCorrespondance() {
  standardizedOperationSheet.value.value!.mappingTables.push({
    id: '',
    paramColumns: [],
    data: [],
  })
}

const fileteredParametersForMappingTable = computed(() => {
  return predefinedParameters
    .concat(standardizedOperationSheet.value.value!.parameters)
    .filter((it) => it.type === 'CHOICE')
})

const allParamIdValid = computed(() => {
  const allParamIds = standardizedOperationSheet.value.value!.parameters.map((it) => it.id)
  const uniqParamIds = uniq(allParamIds)
  return allParamIds.length === uniqParamIds.length
})
const allTablesCorrespondancesIdValid = computed(() => {
  const allIds = standardizedOperationSheet.value.value!.mappingTables.map((it) => it.id)
  const uniqIds = uniq(allIds)
  return allIds.length === uniqIds.length
})

const allIdValid = computed(() => {
  const allParamIds = standardizedOperationSheet.value
    .value!.parameters.map((it) => it.id)
    .concat(standardizedOperationSheet.value.value!.mappingTables.map((it) => it.id))
  const uniqParamIds = uniq(allParamIds)
  return allParamIds.length === uniqParamIds.length
})

const globalError = computed(() => {
  if (allParamIdValid.value && allTablesCorrespondancesIdValid.value && allIdValid.value) {
    return undefined
  } else {
    return 'Tous vos identifiants de paramètres et de tables de correspondances doivent être uniques'
  }
})

function deleteMappingTable(index: number) {
  standardizedOperationSheet.value.value!.mappingTables.splice(index, 1)
}

// formula parsing
const evaluationFormula = ref(emptyValue())
let formulaObject: any = undefined
const onFormulaUpdate = (v: string) => {
  console.debug('onFormulaUpdate')
  evaluationFormula.value.loading = true
  evaluationFormula.value.error = undefined
  debounceFormulaUpdate(v)
}
const debounceFormulaUpdate = debounce((v: string) => {
  console.debug('debounce formula')
  try {
    evaluationFormula.value.loading = false
    formulaObject = new Formula(v)
    console.debug('fqzfq')
    const undefinedVariable = (formulaObject._variables as string[]).find(
      (variable) =>
        !standardizedOperationSheet.value.value!.parameters.find((it) => it.id === variable) &&
        !standardizedOperationSheet.value.value!.mappingTables.find((it) => it.id === variable)
    )
    if (undefinedVariable) {
      evaluationFormula.value.error = "La variable ' " + undefinedVariable + "' n'existe pas"
      return
    }
    evaluationFormula.value.value = true
  } catch (e: unknown) {
    if (e instanceof Error) {
      if (e.message === 'Could not parse formula: Syntax error.') {
        evaluationFormula.value.error = 'Erreur de syntaxe dans la formule'
      } else {
        logException(e)
        evaluationFormula.value.error = "Erreur imprévu lors de l'évaluation de la formule"
      }
    } else {
      logException(e)
      evaluationFormula.value.error = "Erreur imprévu lors de l'évaluation de la formule"
    }
  }
}, 300)

// Preview
const paramPreview = ref<Record<string, any>[]>([{}])
const result = ref<PromisableValue<number>>(succeedValue(0))

watch(
  paramPreview,
  (v) => {
    console.debug('watch paramPreview')
    if (formulaObject) {
      ;(formulaObject._variables as string[]).forEach((variable) => {
        const mappingTable = standardizedOperationSheet.value.value!.mappingTables.find((it) => it.id === variable)
        if (mappingTable) {
          v.forEach((operationLine) => {
            operationLine[variable] =
              mappingTable.data[
                generateMappingTableCombinaisons(mappingTable, fileteredParametersForMappingTable.value).findIndex(
                  (it) =>
                    isEqual(
                      it,
                      mappingTable.paramColumns.map((it) => operationLine[it])
                    )
                )
              ]
          })
        }
      })
      result.value = evaluateFormula(formulaObject, v)
    }
  },
  {
    immediate: true,
  }
)

watch(
  () => standardizedOperationSheet.value.value!.controlOrderNature,
  (v) => {
    if (v == 'HUNDRED_PERCENT') {
      standardizedOperationSheet.value.value = {
        ...standardizedOperationSheet.value.value,
        controlOrderType: 'SITE',
        minimumSatisfyingControlRates: null,
      } as StandardizedOperationSheet
    }
  }
)

// Enregistrement
const formRef = ref<typeof VForm | null>(null)
const saving = ref(emptyValue<StandardizedOperationSheet>())
const validationErrors = ref<{ id: string | number; errorMessages: string[] }[]>()
const save = async () => {
  const validation = await formRef.value!.validate()
  if (!validation.valid) {
    validationErrors.value = validation.errors
  }

  if (validation.valid) {
    await uploadPdfDocument()
    await uploadInternalPdfDocument()

    if (props.id) {
      handleAxiosPromise(
        saving,
        standardizedOperationSheetApi.update(
          props.id,
          mapToStandardizedOperationSheetRequest(standardizedOperationSheet.value.value!)
        ),
        {
          afterSuccess: () => {
            snackbarStore.setSuccess('Fiche Opération bien mise à jour')
            succeedSave()
            certified.value = saving.value.value!.certified
            load()
          },
          afterError: failedSave,
        }
      )
    } else {
      handleAxiosPromise(
        saving,
        standardizedOperationSheetApi.create(
          mapToStandardizedOperationSheetRequest(standardizedOperationSheet.value.value!)
        ),
        {
          afterSuccess: (r) => {
            snackbarStore.setSuccess('Fiche Opération bien créée')
            succeedSave()
            router.push({
              name: 'StandardizedOperationSheetOneView',
              params: { id: r.data.id },
            })
          },
        }
      )
    }
  }
}
const duplicate = async () => {
  if ((await formRef.value!.validate()).valid) {
    unsavedDataDialog.value.confirmCallback = () => {}
    handleAxiosPromise(
      saving,
      standardizedOperationSheetApi.create(
        mapToStandardizedOperationSheetRequest({
          ...standardizedOperationSheet.value.value!,
          visible: false,
          certified: false,
          operationCode: standardizedOperationSheet.value.value!.operationCode + ' - Copie',
          description:
            'Copie de ' +
            standardizedOperationSheet.value.value!.operationCode +
            ' - ' +
            standardizedOperationSheet.value.value!.operationCode,
        })
      ),
      {
        afterSuccess: (r) => {
          snackbarStore.setSuccess('Fiche Opération bien dupliquée')
          succeedSave()
          router.push({
            name: 'StandardizedOperationSheetOneView',
            params: { id: r.data.id },
          })
        },
      }
    )
  }
}

const nOperations = ref(emptyValue<Page<Operation>>())
const hasOperations = computed(() => {
  // TODO fos temporaly editable
  // return (nOperations.value.value?.numberOfElements ?? 0) > 0
  return false
})

const certified = ref(false)

// chargement
const load = async () => {
  const id = props.id
  if (id) {
    await handleAxiosPromise(standardizedOperationSheet, standardizedOperationSheetApi.findOne(id), {
      afterSuccess: () => {
        debounceFormulaUpdate(standardizedOperationSheet.value.value!.formula)
        standardizedOperationSheet.value.value?.parameters.forEach((it) => {
          it.localId = parameterIdAutoIncrement.value++
        })
        certified.value = standardizedOperationSheet.value.value!.certified
        succeedSave()
      },
    })
    handleAxiosPromise(
      nOperations,
      operationApi.findAll(
        {
          standardizedOperationSheetIds: [id],
        },
        { size: 0 }
      )
    )
    succeedSave()
  } else {
    standardizedOperationSheet.value.value = makeEmptyStandardizedOperationSheet()
    standardizedOperationSheet.value.value.epcBonusEligible = true
  }
}
watch(
  () => props.id,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

watch(
  () => standardizedOperationSheet.value.value?.operationCode,
  (v) => {
    if (!props.id && v?.startsWith('BAR-')) {
      standardizedOperationSheet.value.value!.precariousnessBonusEnabled = true
    }
  }
)

const parameterListRef = ref(null)
useSortable(parameterListRef, ref([]), {
  handle: '.sortable-handle',
  onUpdate(e) {
    moveArrayElement(standardizedOperationSheet.value.value!.parameters, e.oldIndex!, e.newIndex!)
  },
})

const getStandardizedOperationSheet = (id: number) => {
  return standardizedOperationSheetApi.findOne(id)
}
const getAllStandardizedOperationSheet = (v: string, pageable: Pageable) => {
  return standardizedOperationSheetApi
    .findAll(pageable, {
      visible: true,
      search: v,
    })
    .then((it) => ({
      ...it,
      data: {
        ...it.data,
        content: it.data.content.filter((it) => it.id !== props.id),
      },
    }))
}

const isControlOrderRuleActive = computed(
  () =>
    standardizedOperationSheet.value.value?.controlOrderExportTemplate ||
    standardizedOperationSheet.value.value?.controlOrderType ||
    standardizedOperationSheet.value.value?.controlOrderNature ||
    standardizedOperationSheet.value.value?.controlOrderStartDate
)

const isControlOrderRuleActiveAndSampleNature = computed(() => {
  return isControlOrderRuleActive.value && standardizedOperationSheet.value.value?.controlOrderNature == 'SAMPLE'
})

const operationToTest = ref<Operation>()
watch(operationToTest, (v) => {
  if (v?.finalAddress?.postalCode) {
    console.debug('operation selected')
    paramPreview.value[0]['region'] = resolveRegion(v.finalAddress.postalCode)
    paramPreview.value[0]['zone_climatique'] = resolveZoneClimatique(v?.finalAddress?.postalCode)
  }
})
const testExportEmmy = () => {
  const operation: Operation = cloneDeep(operationToTest.value!)
  operation.parameterValues = cloneDeep(paramPreview.value)
  if (testPostalCode.value) {
    operation.finalAddress.postalCode = testPostalCode.value
  }
  operation.boostBonusSimulation = null
  operation.epcBonusParameterValues = null
  operation.precariousnessBonusParameterValues = undefined
  operation.standardizedOperationSheet = cloneDeep(standardizedOperationSheet.value!.value!)
  standardizedOperationSheetApi
    .testExportEmmy(operation)
    .then((v) => {
      downloadFile(`export-emmy.csv`, v.data)
    })
    .catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
    })
}

const addMinimumSatisfyingControlRate = () => {
  if (!standardizedOperationSheet.value.value?.minimumSatisfyingControlRates) {
    standardizedOperationSheet.value.value!.minimumSatisfyingControlRates = [makeEmptyMinimumControlRate()]
  } else {
    standardizedOperationSheet.value.value!.minimumSatisfyingControlRates?.push(makeEmptyMinimumControlRate())
  }
}

const deleteMinimumSatisfyingControlRate = (index: number) => {
  standardizedOperationSheet.value.value!.minimumSatisfyingControlRates?.splice(index, 1)
}

const clearUnusedRate = (controlOrderType: ControlOrderType | null) => {
  let clearFunction: (item: MinimumControlRate) => void
  if (controlOrderType == null) {
    clearFunction = (item: MinimumControlRate) => {
      item.contactRate = null
      item.siteRate = null
    }
  } else if (controlOrderType == 'SITE') {
    clearFunction = (item: MinimumControlRate) => {
      item.contactRate = null
    }
  } else if (controlOrderType == 'CONTACT') {
    clearFunction = (item: MinimumControlRate) => {
      item.siteRate = null
    }
  }

  standardizedOperationSheet.value.value?.minimumSatisfyingControlRates?.forEach((item) => {
    clearFunction(item)
  })
}

//Files
const documentToSend = ref(emptyValue<Document>())
const pdfDocumentFile = ref<File>()
const internalPdfDocumentFile = ref<File>()
const uploadPdfDocument = async () => {
  if (pdfDocumentFile.value && pdfDocumentFile.value.size > 0) {
    await handleAxiosPromise(documentToSend, documentApi.create(pdfDocumentFile.value), {
      afterSuccess: (v) => {
        snackbarStore.setSuccess('Le document a bien été ajouté')
        standardizedOperationSheet.value.value!.pdfDocument = v.data
        pdfDocumentFile.value = undefined
      },
      afterError: () => {
        snackbarStore.setError(documentToSend.value.error ?? "Echec de l'envoi du document", 5000)
      },
    })
  }
}
const uploadInternalPdfDocument = async () => {
  if (internalPdfDocumentFile.value && internalPdfDocumentFile.value.size > 0) {
    await handleAxiosPromise(documentToSend, documentApi.create(internalPdfDocumentFile.value), {
      afterSuccess: (v) => {
        snackbarStore.setSuccess('Le document interne a bien été ajouté')
        standardizedOperationSheet.value.value!.internalPdfDocument = v.data
        internalPdfDocumentFile.value = undefined
      },
      afterError: () => {
        snackbarStore.setError(documentToSend.value.error ?? "Echec de l'envoi du document interne", 5000)
      },
    })
  }
}

const codePostalRules = ref<ValidationRule[]>([codePostalRule, requiredRule])
const testPostalCode = ref<string>()
const testPredefinedValues = computed(() => ({
  zone_climatique: resolveZoneClimatique(testPostalCode.value),
  region: resolveRegion(testPostalCode.value),
}))
</script>
