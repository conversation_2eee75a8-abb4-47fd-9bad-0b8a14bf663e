import type { Beneficiary } from './beneficiary'
import type { LocalDate, LocalDateTime } from './date'
import type { Entity } from './entity'
import type { OperationStatus } from './operation'
import type { Subcontractor } from './subcontractor'
import type { User } from './user'
import type { Works, WorksType } from './works'

export interface OperationsGroup {
  id: number
  name: string
  beneficiary: Beneficiary
  subcontractor: Subcontractor | null
  creationUser: User
  creationDateTime: LocalDateTime
  operationsNumber: number
  entity: Entity
  commercialOfferWithoutFinancialIncentive: number | undefined
  customerFinancialIncentive: number | undefined
  onlyEpcOperations: boolean
  documentTypeIdsSent?: number[]
  atypicalClassicValuationValue: number | null
  atypicalPrecariousnessValuationValue: number | null
}

export interface EnhancedOperationsGroup extends OperationsGroup {
  properties: PropertyDto[]
  step: number | null
  status: OperationStatus
}

export interface PropertyDto {
  name: string | null
  code: string | null
  street: string
  city: string
  postalCode: string
}

export interface OperationsGroupRequest
  extends Omit<
    OperationsGroup,
    'id' | 'beneficiary' | 'creationUser' | 'creationDateTime' | 'operationsNumber' | 'entity' | 'subcontractor'
  > {
  beneficiaryId: number
  subcontractorId?: number
  entityId: string
}

export interface ValidateStep30OperationsGroupRequest {
  finalWorksType?: WorksType
  works1?: Works
  works2?: Works
  works3?: Works
  customerOfferNumber: string | null
  offersDispatchDate: LocalDate | null
  selfWorks: boolean
}

export const makeEmptyValidateStep30OperationsGroupRequest = (): ValidateStep30OperationsGroupRequest => ({
  customerOfferNumber: '',
  offersDispatchDate: '',
  selfWorks: false,
})

export interface ValidateStep40OperationsGroupRequest {
  signedDate: LocalDate
  customerDuplicate: boolean
}

export const makeEmptyValidateStep40OperationsGroupRequest = (): ValidateStep40OperationsGroupRequest => ({
  signedDate: '',
  customerDuplicate: false,
})

export interface OperationsGroupSummaryDto {
  operationsGroupId: number
  customerFinancialIncentive: number
  commercialOfferWithoutFinancialIncentive: number
  totalFee: number

  classicCumacSum: number
  precariousnessCumacSum: number
  numberOfOperation: number
  classicValuationAmountSum: number
  precariousnessValuationAmountSum: number
}

export const mapToOperationsGroupRequest = (
  operationsGroup: OperationsGroup,
  complement: Partial<OperationsGroupRequest>
): OperationsGroupRequest => {
  return {
    beneficiaryId: operationsGroup.beneficiary.id,
    subcontractorId: operationsGroup.subcontractor?.id,
    entityId: operationsGroup.entity.id,
    customerFinancialIncentive: operationsGroup.customerFinancialIncentive,
    commercialOfferWithoutFinancialIncentive: operationsGroup.commercialOfferWithoutFinancialIncentive,
    name: operationsGroup.name,
    onlyEpcOperations: operationsGroup.onlyEpcOperations,
    ...complement,
  } as OperationsGroupRequest
}
