<template>
  <slot :is-valid-at-step30="isValidAtStep30">
    <VInput :model-value="modelValue" :rules="rules" hide-details="auto">
      <VCard v-if="modelValue" class="w-100">
        <VCardTitle v-if="withTitle">
          <slot name="title"></slot>
        </VCardTitle>
        <VDivider />
        <VCardText class="pa-2">
          <VRow class="flex-column" :no-gutters="!expanded">
            <VCol>
              <NjDisplayValue label="Raison sociale" :value="modelValue.socialReason" />
            </VCol>
            <VCol>
              <NjDisplayValue
                :color-title="withColor ? (modelValue.siren ? '' : 'warning') : ''"
                :color-value="withColor ? (modelValue.siren ? '' : 'warning') : ''"
                label="Siren"
                :value="modelValue.siren"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Adresse siège social"
                :color-title="withColor ? (isAddressValid ? '' : 'error') : ''"
                :color-value="withColor ? (isAddressValid ? '' : 'error') : ''"
                :value="
                  formatAddressable({
                    city: modelValue.city,
                    postalCode: modelValue.postalCode,
                    street: modelValue.address,
                    country: null,
                  })
                "
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                label="Signataire client"
                :value="(modelValue.firstName ?? '') + ' ' + (modelValue.lastName ?? '')"
              />
            </VCol>
            <VCol>
              <NjDisplayValue label="Fonction du signataire" :value="modelValue.capacity" />
            </VCol>
            <VCol>
              <NjDisplayValue
                :color-title="withColor ? (isPhoneNumberValid ? '' : 'warning') : ''"
                :color-value="withColor ? (isPhoneNumberValid ? '' : 'warning') : ''"
                label="Téléphone"
                :value="modelValue.phoneNumber"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                :color-title="withColor ? (modelValue.email ? '' : 'warning') : ''"
                label="Email"
                :value="modelValue.email"
              />
            </VCol>
            <VCol>
              <NjDisplayValue
                :color-title="withColor ? (modelValue.certified ? '' : 'warning') : ''"
                :color-value="withColor ? (modelValue.certified ? '' : 'warning') : ''"
                label="Certifié"
                :value="modelValue.certified ? 'OUI' : 'NON'"
              />
            </VCol>
            <VDivider />
            <VCol v-if="withHistory">
              <NjExpansionPanel title="Traçabilité" :loading-value="histories.loading">
                <VProgressCircular v-show="histories.loading" indeterminate size="24" color="primary" />
                <VRow dense class="flex-column">
                  <VCol v-for="history in histories.value?.content" :key="history.uuid">
                    <HistoryCard :display-properties="beneficiaryHistoryDisplayProperties" :model-value="history" />
                  </VCol>
                  <VCol v-if="!histories.value?.totalElements">
                    <i>Aucun historique</i>
                  </VCol>
                </VRow>
              </NjExpansionPanel>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
      <div v-else class="w-100">
        <i>Aucun bénéficiaire sélectionné</i>

        <NjDisplayValue v-if="legacy" label="Ancienne valeur" :value="legacy" />
      </div>
    </VInput>
  </slot>
</template>
<script lang="ts" setup>
import type { Beneficiary } from '@/types/beneficiary'
import type { PropType } from 'vue'
import type { ValidationRule } from '@/types/rule'
import { formatAddressable } from '@/types/address'
import { useValidateBeneficiary } from '@/types/beneficiary'
import { beneficiaryHistoryApi } from '@/api/beneficiaryHistory'
import type { VCardTitle, VDivider } from 'vuetify/components'
import { type BeneficiaryHistory, beneficiaryHistoryDisplayProperties } from '@/types/history'
import { type Page } from '@/types/pagination'

const props = defineProps({
  modelValue: {
    type: Object as PropType<Beneficiary | null>,
  },
  rules: {
    type: Array as PropType<Array<ValidationRule>>,
  },
  withColor: Boolean,
  expanded: Boolean,
  legacy: String as PropType<string | null>,
  withTitle: Boolean,
  withHistory: Boolean,
})

const beneficiary = computed(() => props.modelValue)

const { isPhoneNumberValid, isAddressValid, isValidAtStep30 } = useValidateBeneficiary(beneficiary)

const histories = ref(emptyValue<Page<BeneficiaryHistory>>())

watch(
  [() => props.withHistory, () => props.modelValue],
  (v) => {
    histories.value = emptyValue<Page<BeneficiaryHistory>>()
    if (v[0] && v[1]) {
      handleAxiosPromise(
        histories,
        beneficiaryHistoryApi.findAll({ size: 1000, sort: ['creationDateTime,DESC'] }, { beneficiaryId: v[1].id })
      )
    }
  },
  {
    immediate: true,
  }
)

defineExpose({ isValidAtStep30 })
</script>
