<template>
  <component :is="to ? RouterLink : 'a'" :to="to" v-bind="args" class="v-link">
    <VIcon v-if="icon" start class="v-link__icon" :icon="icon" />
    <span class="v-link__content">
      <slot />
    </span>
  </component>
</template>

<script setup lang="ts">
import { merge } from 'lodash'
import type { PropType } from 'vue'
import { type RouteLocationRaw, RouterLink } from 'vue-router'

const props = defineProps({
  to: {
    type: Object as PropType<RouteLocationRaw>,
  },
  href: {
    type: String,
  },
  icon: {
    type: String,
  },
})

const attrs = useAttrs()

const args = computed(() => {
  const result: any = {}
  merge(result, attrs)
  if (props.href) {
    result.href = props.href
  }
  return result
})
</script>

<style lang="scss">
.v-link {
  color: #009de9;
  display: inline-block;
  // border-bottom: 1px solid #009DE9;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    color: #0b74c0;

    .v-link__content {
      border-bottom-color: #0b74c0;
      border-bottom-width: 2px;
    }
  }
}

.v-link__content {
  border-bottom: 1px solid #009de9;
}

.v-link__icon {
}
</style>
