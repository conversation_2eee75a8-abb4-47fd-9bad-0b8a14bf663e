<template>
  <NjPage>
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value!"
        :on-click-row="(item: ControlOrderBatchDto) => router.push(routeToOneView(item))"
        :to-row="routeToOneView"
        fixed
        @update:pageable="updatePageable"
      >
        <template #[`item.standardizedOperationSheets`]="{ item }">
          <div>
            {{ item.standardizedOperationSheetCodes[0] ?? '' }}
            {{
              item.standardizedOperationSheetCodes.length > 1
                ? `, +${item.standardizedOperationSheetCodes.length - 1}`
                : ''
            }}
            <VTooltip v-if="item.standardizedOperationSheetCodes.length > 1" location="top" activator="parent">
              {{ item.standardizedOperationSheetCodes.join(', ') }}
            </VTooltip>
          </div>
        </template>
        <template #[`item.step`]="{ item }">
          {{ controlOrderBatchStepItems.find((i) => i.value == item.controlOrderBatch.step)?.title }}
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>
<script setup lang="ts">
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import { controlOrderBatchApi, type ControlOrderBatchFilter } from '@/api/controlOrderBatchApi'
import { controlOrderBatchStepItems, type ControlOrderBatchDto } from '@/types/controlOrder'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import { formatNumber } from '@/types/format'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import router from '@/router'

const headers: DataTableHeader[] = [
  {
    title: 'Numéro de lot',
    value: 'batchCode',
    formater: (item) => item.controlOrderBatch.batchCode,
  },
  {
    title: "Type d'OS",
    value: 'standardizedOperationSheet.operationCode',
    sortable: false,
  },
  {
    title: 'Date de création',
    value: 'creationDateTime',
    formater: (item) => formatHumanReadableLocalDateTime(item.controlOrderBatch.creationDateTime),
  },
  {
    title: 'Organisme de contrôle',
    value: 'controlOrderBatch.controlOrganism.socialReason',
    sortable: false,
  },
  {
    title: "Nombre d'opération",
    value: 'operationsNumber',
    sortable: false,
  },
  {
    title: 'Volume classique',
    value: 'classicCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Volume précarité',
    value: 'precariousnessCumac',
    formater: (_, value) => formatNumber(value),
    cellClass: 'text-right',
  },
  {
    title: 'Volume total',
    value: 'totalCumac',
    formater: (item) => formatNumber(item.classicCumac + item.precariousnessCumac),
    cellClass: 'text-right',
    sortable: false,
  },
  {
    title: 'Étape',
    value: 'step',
    onclick: () => {},
  },
  {
    title: 'Taux de satisfaction sur site',
    value: 'siteComplianceRate',
    formater: (item) =>
      item.controlOrderBatch.siteComplianceRate ? formatNumber(item.controlOrderBatch.siteComplianceRate) + ' %' : '',
  },
  {
    title: 'Taux de satisfaction contact',
    value: 'contactComplianceRate',
    formater: (item) =>
      item.controlOrderBatch.contactComplianceRate
        ? formatNumber(item.controlOrderBatch.contactComplianceRate) + ' %'
        : '',
  },
]

const { data, pageable, updatePageable } = usePagination<ControlOrderBatchDto, ControlOrderBatchFilter>(
  (filter, pageable) => controlOrderBatchApi.findAll(filter, pageable),
  {},
  {
    sort: ['creationDateTime,DESC'],
  }
)

const routeToOneView = (item: ControlOrderBatchDto) => ({
  name: 'ControlOrderBatchOneView',
  params: { id: item.controlOrderBatch.id },
})
</script>
