<template>
  <NjPage title="Ventes CEE">
    <template #header-actions>
      <NjBtn :loading="loadingExport" @click="exportCeeSale">Export Excel</NjBtn>
    </template>
    <template #body>
      <VRow class="flex-column content-layout h-100" dense>
        <VCol class="flex-grow-0">
          <VRow dense>
            <VCol>
              <SearchInput v-model="filter.chronoCode" label="Chrono" prepend-inner-icon="mdi-magnify" />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="filter.emmyFolderIds"
                label="Dossier EMMY"
                :query-for-one="(id) => emmyFolderApi.findById(id).then((it) => ({ ...it, data: it.data.emmyFolder }))"
                :query-for-all="
                  (s, pageable) =>
                    emmyFolderApi.findAll(
                      { search: s, periodIds: filter.periodIds },
                      { ...pageable, sort: ['pnceeIssuedDate,ASC'] } // chronologique car on cherche à vendre les plus anciens
                    )
                "
                :item-title="(item) => item.emmyCode + ' - ' + item.name"
                chips
                closable-chips
                multiple
                infinite-scroll
                class="bg-white"
              />
            </VCol>
            <VCol>
              <VSelect
                v-model="filter.eligibleForSale"
                label="Vente de CEE autorisée"
                :items="allowedEntityToSellCumacItems"
                class="bg-white"
              />
            </VCol>
            <VCol>
              <RemoteAutoComplete
                v-model="filter.entityIds"
                label="Agence"
                :query-for-one="(id) => entityApi.getOne(id)"
                :query-for-all="
                  (s, pageable) =>
                    entityApi.getAll({ search: s, visible: true, enabled: true, canSell: true }, pageable)
                "
                :item-title="(item) => item.name + ' (' + item.id + ')'"
                multiple
                infinite-scroll
                class="bg-white"
                chips
                closable-chips
              >
              </RemoteAutoComplete>
            </VCol>
            <VCol>
              <VSelect
                v-model="filter.periodIds"
                label="Périodes"
                :items="periodsStore.periods"
                item-title="name"
                item-value="id"
                multiple
                class="bg-white"
              />
            </VCol>

            <VCol>
              <VSelect v-model="cumacTypesSelected" label="Type de cumac" :items="cumacTypesSelect" class="bg-white" />
            </VCol>
          </VRow>
        </VCol>
        <VCol class="flex-grow-0">
          <VRow dense>
            <VCol>
              <NjDatePicker v-model="filter.endedAfter" label="Début" />
            </VCol>
            <VCol>
              <NjDatePicker v-model="filter.endedBefore" label="Fin" />
            </VCol>
            <VCol>
              <VTextField
                label="Valorisation classique minimum"
                class="bg-white"
                clearable
                suffix="€/MWhc"
                :rules="[emptyOrNumericRule]"
                @update:model-value="
                  (v) => (filter.goeClassicValuationValue = numericRule(v) == true ? parseFloat(v) : undefined)
                "
              />
            </VCol>
            <VCol>
              <VTextField
                label="Valorisation classique maximum"
                class="bg-white"
                clearable
                suffix="€/MWhc"
                :rules="[emptyOrNumericRule]"
                @update:model-value="
                  (v) => (filter.loeClassicValuationValue = numericRule(v) == true ? parseFloat(v) : undefined)
                "
              />
            </VCol>
            <VCol>
              <VTextField
                label="Valorisation précarité minimum"
                class="bg-white"
                clearable
                suffix="€/MWhc"
                :rules="[emptyOrNumericRule]"
                @update:model-value="
                  (v) => (filter.goePrecariousnessValuationValue = numericRule(v) == true ? parseFloat(v) : undefined)
                "
              />
            </VCol>
            <VCol>
              <VTextField
                label="Valorisation précarité maximum"
                class="bg-white"
                clearable
                suffix="€/MWhc"
                @update:model-value="
                  (v) => (filter.loePrecariousnessValuationValue = numericRule(v) == true ? parseFloat(v) : undefined)
                "
              />
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="h-100" dense>
            <VCol :cols="userStore.canSellCee ? 8 : 12">
              <VCard class="flex-column content-layout h-100">
                <VCardText>
                  <VRow class="flex-column content-layout h-100" dense>
                    <VCol>
                      <NjDataTable
                        v-model:selections="selection"
                        :pageable="pageable"
                        :page="data.value!"
                        :headers="ceeSalesOperationHeaders"
                        :on-click-row="clickRow"
                        :clicked-row="clickedRow"
                        :loading="data.loading"
                        fixed
                        checkboxes
                        multi-selection
                        :page-sizes="[10, 20, 50, 100]"
                        @update:pageable="updatePageable"
                      />
                    </VCol>
                    <VCol class="flex-grow-0">
                      <VRow class="align-center">
                        <VCol><b>Dispo. Classique:</b> {{ formatNumber(availableClassicCumac) }} </VCol>
                        <VCol><b>Dispo. Précarité:</b> {{ formatNumber(availablePrecariousnessCumac) }} </VCol>
                        <VCol>
                          <b>Dispo. total:</b> {{ formatNumber(availableClassicCumac + availablePrecariousnessCumac) }}
                        </VCol>
                        <VCol> <b>Opérations sélectionnés: </b> {{ formatNumber(selection.length) }} </VCol>
                      </VRow>
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>
            <VCol v-if="userStore.canSellCee" cols="4">
              <VCard class="content-layout">
                <VCardTitle class="content-layout__header">Vente</VCardTitle>
                <VDivider />
                <VCardText class="content-layout__main">
                  <NjExpansionPanel title="Vente de MWhc">
                    <VForm ref="formRef">
                      <VRow class="flex-column" dense>
                        <VCol>
                          <NjDisplayValue label="Date">
                            <template #value>
                              <div class="w-50">
                                <NjDatePicker
                                  v-model="saleForm.saleDate"
                                  :rules="[requiredRule, isEmptyOrBeforeToday]"
                                />
                              </div>
                            </template>
                          </NjDisplayValue>
                        </VCol>
                        <VCol>
                          <VTextField v-model="saleForm.buyer" label="Acheteur" :rules="[requiredRule]" />
                        </VCol>
                        <VCol>
                          <VTextField v-model="saleForm.comment" label="Remarque" />
                        </VCol>
                        <VCol>
                          <VTextField
                            v-model="saleForm.unitSellingPrice"
                            label="Prix de vente du MWhc"
                            type="number"
                            suffix="€/MWhc"
                            :rules="[requiredRule, positiveOrNullNumericRuleGenerator()]"
                          />
                        </VCol>
                      </VRow>
                    </VForm>
                  </NjExpansionPanel>
                  <NjExpansionPanel title="kWhc disponibles pour la vente">
                    <VRow class="flex-column" dense>
                      <VCol>
                        <VRow>
                          <VCol class="d-flex align-center">
                            <NjDisplayValue
                              label="Classique"
                              color-value="primary"
                              :value="formatNumber(availableClassicCumac)"
                            />
                          </VCol>
                          <VCol cols="4">
                            <NjBtn
                              class="w-100"
                              :disabled="availableClassicCumac === 0"
                              @click="() => initiateSale(true, false)"
                            >
                              Vendre Classique
                            </NjBtn>
                          </VCol>
                        </VRow>
                      </VCol>
                      <VCol>
                        <VRow>
                          <VCol class="d-flex align-center">
                            <NjDisplayValue
                              label="Précarité"
                              color-value="primary"
                              :value="formatNumber(availablePrecariousnessCumac)"
                            />
                          </VCol>
                          <VCol cols="4">
                            <NjBtn
                              class="w-100"
                              :disabled="availablePrecariousnessCumac === 0"
                              @click="() => initiateSale(false, true)"
                            >
                              Vendre Précarité
                            </NjBtn>
                          </VCol>
                        </VRow>
                      </VCol>
                      <VCol>
                        <VRow>
                          <VCol class="d-flex align-center">
                            <NjDisplayValue
                              label="Classique et Précarité"
                              color-value="primary"
                              :value="formatNumber(availableClassicCumac + availablePrecariousnessCumac)"
                            />
                          </VCol>
                          <VCol cols="4">
                            <NjBtn
                              class="w-100"
                              :disabled="availableClassicCumac + availablePrecariousnessCumac === 0"
                              @click="() => initiateSale(true, true)"
                            >
                              Vendre Class. et Préc.
                            </NjBtn>
                          </VCol>
                        </VRow>
                      </VCol>
                    </VRow>
                    <CardDialog
                      v-model="sellCeeDialog"
                      title="Vente de kWhc"
                      :width="hideClassic || hidePrecariousness ? '50%' : '70%'"
                      :persistant="selling"
                    >
                      <VRow class="flex-column">
                        <VCol>
                          <VRow>
                            <VCol cols="8">
                              Veuillez spécifier la quantité de kWhc que vous voulez vendre pour chaque opération
                            </VCol>
                            <VCol cols="4">
                              <SearchInput v-model="sellFilter" />
                            </VCol>
                          </VRow>
                        </VCol>
                        <VCol>
                          <VRow class="align-center">
                            <VCol class="value__label text-center">Chrono</VCol>
                            <VCol class="value__label text-center">Nom Opération</VCol>
                            <VCol v-if="!hideClassic" class="value__label text-center"> kWhc Class. Dispo. </VCol>
                            <VCol v-if="!hidePrecariousness" class="value__label text-center">kWhc Préca. Dispo.</VCol>
                            <VCol v-if="!hideClassic"> </VCol>
                            <VCol v-if="!hidePrecariousness"> </VCol>
                          </VRow>
                        </VCol>
                        <VCol v-for="ope in filteredOperationToSell" :key="ope.id">
                          <VRow class="align-center">
                            <VCol class="text-center">{{ ope.chronoCode }}</VCol>
                            <VCol class="text-center">{{ ope.operationName }}</VCol>
                            <VCol v-if="!hideClassic" class="value__value text-center">
                              {{ formatNumber(ope.availableClassicCumac) }}
                            </VCol>
                            <VCol v-if="!hidePrecariousness" class="value__value text-center">
                              {{ formatNumber(ope.availablePrecariousnessCumac) }}
                            </VCol>
                            <VCol v-if="!hideClassic">
                              <VTextField
                                v-model.number="quantitiesSold[ope.id].soldClassicCumac"
                                label="kWhc Class. à Vendre"
                                :rules="[
                                  hidePrecariousness
                                    ? positiveNumericRuleGenerator()
                                    : positiveOrNullNumericRuleGenerator(),
                                  inferiorToRule(ope.availableClassicCumac),
                                ]"
                              />
                            </VCol>
                            <VCol v-if="!hidePrecariousness">
                              <VTextField
                                v-model.number="quantitiesSold[ope.id].soldPrecariousnessCumac"
                                label="kWhc Préca. à Vendre"
                                :rules="[
                                  hideClassic ? positiveNumericRuleGenerator() : positiveOrNullNumericRuleGenerator(),
                                  inferiorToRule(ope.availablePrecariousnessCumac),
                                ]"
                              />
                            </VCol>
                          </VRow>
                        </VCol>
                        <VCol v-if="filteredOperationToSell.length === 0">
                          <g v-if="operationToSell.length === 0">
                            Vous devez sélectionner des opérations dans le tableau précédent.
                          </g>
                          <i v-else> Aucun élément trouvé. </i>
                        </VCol>
                      </VRow>
                      <template #pre-actions>
                        <div>
                          <div v-if="!hideClassic">
                            Totaux Classique à vendre :
                            <b
                              >{{ formatKwhcNumber(toSellClassicCumac)
                              }}<template v-if="toSellClassicCumac !== availableClassicCumac"
                                >/{{ formatKwhcNumber(availableClassicCumac) }}</template
                              ></b
                            >
                          </div>
                          <div v-if="!hidePrecariousness">
                            Totaux Précarité à vendre :
                            <b
                              >{{ formatKwhcNumber(toSellPrecariousnessCumac)
                              }}<template v-if="toSellPrecariousnessCumac !== availablePrecariousnessCumac"
                                >/{{ formatKwhcNumber(availablePrecariousnessCumac) }}</template
                              ></b
                            >
                          </div>
                        </div>
                      </template>
                      <template #actions>
                        <NjBtn variant="outlined" :disabled="selling" @click="sellCeeDialog = false">Annuler</NjBtn>
                        <NjBtn :loading="selling" @click="sellCee">Valider</NjBtn>
                      </template>
                    </CardDialog>
                  </NjExpansionPanel>
                </VCardText>
                <VDivider />
                <VCardActions class="content-layout__footer">
                  <NjBtn class="mx-auto" @click="initiateVolumeSale">Vendre un volume</NjBtn>
                  <CeeSalesVolumeDialog
                    v-model="sellCeeVolumeDialog"
                    v-model:selection="selection"
                    :form="saleForm"
                    :filter="pageFilter"
                    :pageable="pageable"
                    @sold="reset"
                  />
                </VCardActions>
              </VCard>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="h-100 w-100">
            <VCol class="h-100">
              <VCard class="content-layout">
                <VTabs v-model="tab" class="content-layout__header">
                  <VTab>Ventes pour la ligne de réservation sélectionnée</VTab>
                  <VTab>Ventes pour la date sélectionnée</VTab>
                  <VTab>Stock CEE + Montant des ventes de kWhc CEE/Date</VTab>
                </VTabs>
                <VDivider />
                <VCardText class="content-layout__main mt-4">
                  <VRow class="flex-coxlumn content-layout">
                    <VCol class="content-layout__main pt-0">
                      <VWindow v-model="tab" class="h-100">
                        <VWindowItem :class="tab === 0 ? 'content-layout' : ''">
                          <CeeSalesDetailView
                            v-if="clickedRow"
                            :operation-id="clickedRow?.id"
                            @reload="
                              () => {
                                reload()
                                saleByDateRef?.reloadTables()
                              }
                            "
                          />
                          <span v-else>Aucune opération sélectionnée</span>
                        </VWindowItem>
                        <VWindowItem :class="tab === 1 ? 'content-layout' : ''">
                          <CeeSalesDetailView
                            v-if="saleForm.saleDate"
                            ref="saleDetailRef"
                            :sale-date="saleForm.saleDate"
                            @reload="
                              () => {
                                reload()
                                saleByDateRef?.reloadTables()
                              }
                            "
                          />
                          <span v-else>Aucune date sélectionnée</span>
                        </VWindowItem>
                        <VWindowItem :class="tab === 2 ? 'content-layout' : ''">
                          <CeeSalesByDateView
                            ref="saleByDateRef"
                            @display-date="(date) => displayDetail(date)"
                            @reload="
                              () => {
                                reload()
                                saleDetailRef?.reload()
                              }
                            "
                          />
                        </VWindowItem>
                      </VWindow>
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { emmyFolderApi } from '@/api/emmyFolder'
import { entityApi } from '@/api/entity'
import type { CumacType, OperationFilter } from '@/api/operation'
import NjPage from '@/components/NjPage.vue'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { mapToOperationCeeSaleRequest, type OperationCeeSaleRequest } from '@/types/ceeSale'
import type { LocalDate } from '@/types/date'
import { formatNumber } from '@/types/format'
import type { Operation } from '@/types/operation'
import { formatKwhcNumber } from '@/types/format'
import type { OperationSummary } from '@/types/operationSummary'
import {
  emptyOrNumericRule,
  inferiorToRule,
  isEmptyOrBeforeToday,
  numericRule,
  positiveNumericRuleGenerator,
  positiveOrNullNumericRuleGenerator,
  requiredRule,
} from '@/types/rule'
import { debounce, isEqual } from 'lodash'
import { VCard, VCardText, VCol, VRow, VSelect, VTextField } from 'vuetify/components'
import type { VForm } from 'vuetify/components/VForm'
import useClearSideBar from '../clearSidebar'
import { ceeSalesOperationHeaders } from './ceeSaleOperationHeaders'
import CeeSalesByDateView from './CeeSalesByDateView.vue'
import CeeSalesDetailView from './CeeSalesDetailView.vue'
import CeeSalesVolumeDialog from './CeeSalesVolumeDialog.vue'
import SearchInput from '@/components/SearchInput.vue'

const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

const filter = ref<OperationFilter>({
  endedAfter: undefined,
  endedBefore: undefined,
  operationStatuses: ['DONE'],
  chronoCode: '',
  emmyFolderIds: [],
  entityIds: [],
  periodIds: [],
  cumacTypes: [],
  eligibleForSale: undefined,
  goeClassicValuationValue: undefined,
  loeClassicValuationValue: undefined,
})

const summary = ref<OperationSummary>()
const { data, pageable, updatePageable, updateFilter, pageFilter, reload } = usePagination<Operation, OperationFilter>(
  (filter, pageable) =>
    operationApi.findAll(filter, pageable).then((it) => {
      return operationApi.getSummary(filter).then((summaryResponse) => {
        summary.value = summaryResponse.data
        return it
      })
    }),
  { ...filter.value },
  { sort: ['lastValidatedStepDateTime,ASC'] }
)

const selling = ref(false)
const selection = ref<Operation[]>([])

const cumacTypesSelected = ref('')
const cumacTypesSelect = ref([
  {
    title: 'Classique',
    value: 'CLASSIC',
  },
  {
    title: 'Précarité',
    value: 'PRECARIOUSNESS',
  },
  {
    title: 'Classique ou Précarité',
    value: '',
  },
  {
    title: 'Classique et Précarité',
    value: 'both',
  },
])

const periodsStore = usePeriodsStore()
const tab = ref(0)
const saleDetailRef = ref<typeof CeeSalesDetailView | null>()
const saleByDateRef = ref<typeof CeeSalesByDateView | null>()
const formRef = ref<typeof VForm | null>(null)
const saleForm = ref({
  saleDate: '',
  buyer: undefined,
  comment: '',
  unitSellingPrice: undefined,
})
const sellCeeDialog = ref(false)
const sellCeeVolumeDialog = ref(false)
const hideClassic = ref(false)
const hidePrecariousness = ref(false)
const quantitiesSold = ref<
  Record<
    number,
    {
      soldClassicCumac: number
      soldPrecariousnessCumac: number
    }
  >
>({})

const clickedRow = ref<Operation>()
const clickRow = async (item: Operation) => {
  if (item.id !== clickedRow.value?.id) {
    clickedRow.value = item
  } else {
    clickedRow.value = undefined
  }
}

const displayDetail = (date: LocalDate) => {
  saleForm.value.saleDate = date
  tab.value = 1
}

const initiateSale = async (isClassic: boolean, isPrecariousness: boolean) => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    hideClassic.value = !isClassic
    hidePrecariousness.value = !isPrecariousness
    quantitiesSold.value = selection.value.reduce<typeof quantitiesSold.value>((acc, ope) => {
      acc[ope.id] = {
        soldClassicCumac: hideClassic.value ? 0 : ope.availableClassicCumac,
        soldPrecariousnessCumac: hidePrecariousness.value ? 0 : ope.availablePrecariousnessCumac,
      }
      return acc
    }, {})
    sellCeeDialog.value = true
  } else {
    scrollToFirstError()
  }
}

const initiateVolumeSale = async () => {
  const validation = await formRef.value!.validate()
  if (validation.valid) {
    sellCeeVolumeDialog.value = true
  } else {
    scrollToFirstError()
  }
}

const reset = () => {
  selection.value = []
  sellCeeDialog.value = false
  sellCeeVolumeDialog.value = false
  clickedRow.value = undefined
  reload()
  saleByDateRef.value?.reloadTables()
}

const sellCee = () => {
  const requests: OperationCeeSaleRequest[] = []
  operationToSell.value.forEach((ope) =>
    requests.push(mapToOperationCeeSaleRequest(ope.id, quantitiesSold.value[ope.id], saleForm.value))
  )
  selling.value = true
  ceeSaleApi
    .createFiltered(requests)
    .then(() => {
      snackbarStore.setSuccess(
        `${requests.length === 1 ? 'La vente a été créée' : 'Les ventes ont été créées'} avec succès`
      )
      reset()
    })
    .catch(async (err) =>
      snackbarStore.setError(
        await handleAxiosException(err, undefined, {
          defaultMessage: "Une erreur est survenue lors de la création d'une vente",
        })
      )
    )
    .finally(() => {
      selling.value = false
    })
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    if (!isEqual(v, pageFilter.value)) {
      debounceFilter(v)
    }
  },
  {
    deep: true,
  }
)

watch(cumacTypesSelected, (v) => {
  filter.value.cumacTypes = v ? (v === 'both' ? ['CLASSIC', 'PRECARIOUSNESS'] : [v as CumacType]) : []
})

const availableClassicCumac = computed(() => {
  if (selection.value.length > 0) {
    return selection.value.reduce((prev, next) => prev + next.availableClassicCumac, 0)
  } else {
    return summary.value?.availableClassicCumacSum ?? 0
  }
})
const availablePrecariousnessCumac = computed(() => {
  if (selection.value.length > 0) {
    return selection.value.reduce((prev, next) => prev + next.availablePrecariousnessCumac, 0)
  } else {
    return summary.value?.availablePrecariousnessCumacSum ?? 0
  }
})
const toSellClassicCumac = computed(() => {
  if (selection.value.length > 0) {
    return selection.value.reduce((prev, next) => prev + quantitiesSold.value[next.id].soldClassicCumac, 0)
  } else {
    return summary.value?.availableClassicCumacSum ?? 0
  }
})
const toSellPrecariousnessCumac = computed(() => {
  if (selection.value.length > 0) {
    return selection.value.reduce((prev, next) => prev + quantitiesSold.value[next.id].soldPrecariousnessCumac, 0)
  } else {
    return summary.value?.availablePrecariousnessCumacSum ?? 0
  }
})

const operationToSell = computed(() => {
  if (hideClassic.value) {
    return selection.value.filter((ope) => ope.availablePrecariousnessCumac !== 0)
  }
  if (hidePrecariousness.value) {
    return selection.value.filter((ope) => ope.availableClassicCumac !== 0)
  }
  return selection.value.filter((ope) => ope.availablePrecariousnessCumac !== 0 || ope.availableClassicCumac !== 0)
})

const sellFilter = ref('')
const filteredOperationToSell = computed(() => {
  if (sellFilter.value) {
    return operationToSell.value.filter(
      (it) =>
        it.chronoCode.includes(sellFilter.value) ||
        it.operationName.toLocaleLowerCase().includes(sellFilter.value.toLocaleLowerCase())
    )
  }
  return operationToSell.value
})

const loadingExport = ref(false)
const exportCeeSale = () => {
  loadingExport.value = true
  ceeSaleApi
    .export(pageFilter.value)
    .then((res) => {
      downloadFile('Export vente capte.xlsx', res.data)
    })
    .catch(async (err) => {
      snackbarStore.setError(
        (await handleAxiosException(err, JSON.parse(await err.response.data.text()).message)) ??
          "Erreur lors de l'export des ventes"
      )
    })
    .finally(() => (loadingExport.value = false))
}

const allowedEntityToSellCumacItems = [
  {
    title: 'Oui',
    value: true,
  },
  {
    title: 'Non',
    value: false,
  },
  {
    title: 'Tous',
    value: null,
  },
]

useClearSideBar()
</script>
