<template>
  <VNavigationDrawer location="right" width="500" permanent>
    <VCard class="content-layout">
      <VCardTitle class="d-flex align-center content-layout__header">
        <span class="d-flex w-100"> Filtres </span>
        <NjIconBtn icon="mdi-window-close" variant="flat" color="primary" @click="emit('update:model-value', false)" />
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main">
        <VRow class="flex-column" dense>
          <slot name="more" />
          <VCol v-if="!forSimulation" class="d-flex flex-row">
            <VCheckbox v-model="filter.warning" label="Alertes" :disabled="checkingValuation" class="flex-grow-0" />
            <VCheckbox
              v-model="checkingValuation"
              label="Mode Valorisation"
              :disabled="filter.warning"
              class="flex-grow-0"
            />
          </VCol>
          <VCol v-if="mode === 'osh'" class="d-flex flex-row">
            <VCheckbox
              v-model="filter.oshHidden"
              label="Afficher les opérations masquées"
              :disabled="checkingValuation"
              class="flex-grow-0"
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.stepIds" label="Étapes" :items="steps" multiple clearable chips closable-chips>
              <template #item="{ item }">
                <VListItem @click="updateStepIds(item.raw.id)">
                  <template #prepend>
                    <VCheckboxBtn :model-value="(filter.stepIds ?? []).includes(item.raw.id)"></VCheckboxBtn>
                  </template>
                  {{ item.raw.id }} - {{ item.raw.name }}
                </VListItem>
              </template>
            </VSelect>
          </VCol>
          <VCol>
            <VSelect
              v-model="filter.periodIds"
              label="Périodes"
              :items="periodsStore.periods"
              item-title="name"
              item-value="id"
              multiple
              clearable
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.atypical" label="Atypique" :items="yesNoOptions" clearable />
          </VCol>
          <VCol>
            <VSelect
              v-model="filter.valuationTypeIds"
              label="Type d'affaires"
              :items="valuationTypes.value?.content"
              item-title="name"
              item-value="id"
              multiple
              clearable
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.operationStatuses" label="Processus" :items="operationStatus" multiple clearable>
              <template #selection="{ item }">{{ mapToReadableStatus(item.raw) }}</template>
              <template #item="{ item }">
                <VListItem @click="updateCommercialStatuses(item.raw)">
                  <template #prepend>
                    <VCheckboxBtn :model-value="filter.operationStatuses!.includes(item.raw)"></VCheckboxBtn>
                  </template>
                  {{ mapToReadableStatus(item.raw) }}
                </VListItem>
              </template>
            </VSelect>
          </VCol>
          <VCol v-if="mode !== 'osh'">
            <VSelect
              v-model="filter.commercialStatuses"
              label="Statut offres commerciales"
              :items="commercialStatusTitle"
              multiple
              clearable
            />
          </VCol>
          <VCol>
            <VSelect
              label="Soumise à arrêté contrôle"
              :model-value="controlOrderNatures"
              :items="controlOrderNatureItems"
              multiple
              clearable
              @update:model-value="updateControlOrderNatures"
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedBeneficiaries"
              label="Bénéficiaires"
              :query-for-one="(id) => beneficiaryApi.findOne(id)"
              :query-for-all="(s, pageable) => beneficiaryApi.findAll(pageable, { search: s })"
              :item-title="(item) => (item.lastName ?? '') + ' ' + (item?.socialReason ?? '')"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
              clearable
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedInstructors"
              label="Instructeur"
              :query-for-one="(id) => userApi.getOne(id)"
              :query-for-all="
                (s, pageable) => userApi.getAll(pageable, { search: s, roles: ['INSTRUCTEUR'], active: true })
              "
              :item-title="(item: User) => displayFullnameUser(item)"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
              clearable
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="selectedOperationsGroup"
              label="Regroupement"
              :query-for-one="(id) => operationsGroupApi.findById(id)"
              :query-for-all="(s, pageable) => operationsGroupApi.findAll({ search: s }, pageable)"
              :item-title="(item) => item.name"
              chips
              closable-chips
              multiple
              return-object
              infinite-scroll
              clearable
            />
          </VCol>
          <VCol v-if="userStore.hasRole('ADMIN_PLUS') && mode !== 'osh'">
            <VTextField
              v-model="filter.minimumMonthsOld"
              label="Etape en cours depuis au moins"
              suffix="mois"
              clearable
            />
          </VCol>
          <VCol v-if="mode === 'osh'">
            <VSelect
              label="Délai instruction"
              :items="instructionDelayFilerItems"
              :model-value="filter.instructionDelay"
              clearable
              @update:model-value="updateInstructionDelay"
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.headOperation" label="Opération Chapeau" :items="yesNoOptions" clearable />
          </VCol>
          <VCol>
            <VTextField v-model="filter.ademeCode" label="Numéro Ademe" clearable />
          </VCol>
          <VCol>
            <NjRangeDatePicker
              v-model:min-date="filter.minCommitmentDate"
              v-model:max-date="filter.maxCommitmentDate"
              label="Date d'engagement"
              clearable
            />
          </VCol>
          <VCol>
            <NjRangeDatePicker
              v-model:min-date="filter.minActualEndWorksDate"
              v-model:max-date="filter.maxActualEndWorksDate"
              label="Date de Fin de Travaux"
            />
          </VCol>
          <VCol>
            <RemoteAutoComplete
              v-model="filter.territoryIds"
              label="Territoire"
              :query-for-one="(v: number) => territoryApi.findOne(v)"
              :query-for-ones="(ids: number[]) => territoryApi.findAll({ ids }, { size: 1000 })"
              :query-for-all="
                (v: string, pageable: Pageable) =>
                  territoryApi.findAll({ search: v }, { ...pageable, sort: ['description,ASC'] })
              "
              :item-title="formatTerritory"
              item-value="id"
              infinite-scroll
              chips
              closable-chips
              clearable
              multiple
            />
          </VCol>
          <VCol>
            <NjRangeDatePicker
              v-model:min-date="filter.minEstimatedEndWorksDate"
              v-model:max-date="filter.maxEstimatedEndWorksDate"
              label="Date de Fin de Travaux estimée"
            />
          </VCol>
          <VCol>
            <VSelect v-model="filter.mustSendFinalVersion" label="VF à envoyer" :items="yesNoOptions" clearable />
          </VCol>
          <VCol>
            <VSelect v-model="filter.inOperationsGroup" label="Dans un regroupement" :items="yesNoOptions" clearable />
          </VCol>
          <VCol>
            <VSelect v-model="filter.noEmmyFolder" label="Sans dossier EMMY" :items="yesNoOptions" clearable />
          </VCol>
          <VCol>
            <VDivider />
          </VCol>
          <VCol>
            <div class="d-flex align-center fill-width">
              <div class="text-section-title">FOS</div>
              <VSpacer />
              <VLink
                size="small"
                icon="mdi-format-list-bulleted"
                style="font-weight: initial; font-size: initial"
                @click="fosFilter = true"
              >
                FOS
              </VLink>
            </div>
          </VCol>
          <VCol v-if="selectedFos.length">
            <VList>
              <VListItem v-for="fos in selectedFos" :key="fos.id" density="compact" class="py-0">
                {{ fos.operationCode }}
                <template #append>
                  <NjIconBtn icon="mdi-delete-outline" @click="removeFos(fos)" />
                </template>
              </VListItem>
            </VList>
          </VCol>
          <VCol>
            <VDivider />
          </VCol>
          <VCol>
            <div class="d-flex align-center fill-width">
              <div class="text-section-title">Organisations</div>
              <VSpacer />
              <VLink
                size="small"
                icon="mdi-format-list-bulleted"
                style="font-weight: initial; font-size: initial"
                @click="orgFilter = true"
              >
                Organisations
              </VLink>
            </div>
          </VCol>
          <VCol v-if="selectedEntities.length">
            <VList>
              <VListItem v-for="org in selectedEntities" :key="org.id" density="compact" class="py-0">
                {{ org.name }}
                <template #append>
                  <NjIconBtn icon="mdi-delete-outline" @click="removeEntity(org)" />
                </template>
              </VListItem>
            </VList>
          </VCol>
          <VCol>
            <VDivider />
          </VCol>
          <VCol v-if="!forSimulation">
            <div class="d-flex align-center fill-width">
              <div class="text-section-title">Dossiers EMMY</div>
              <VSpacer />
              <VLink
                size="small"
                icon="mdi-format-list-bulleted"
                style="font-weight: initial; font-size: initial"
                @click="emmyFolderDialog = true"
              >
                Dossiers EMMY
              </VLink>
            </div>
            <EmmyFolderFilterDialog
              v-model="emmyFolderDialog"
              v-model:selected="selectedEmmyFolder"
              title="Sélectionner un ou plusieurs dossiers EMMY"
            />
          </VCol>
          <VCol v-if="selectedEmmyFolder.length">
            <VList>
              <VListItem v-for="folder in selectedEmmyFolder" :key="folder.id" density="compact" class="py-0">
                {{ folder.name }}
                <template #append>
                  <NjIconBtn icon="mdi-delete-outline" @click="removeEmmyFolder(folder)" />
                </template>
              </VListItem>
            </VList>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <StandardizedOperationSheetFilterDialog v-model="fosFilter" v-model:selected="selectedFos" />
    <EntityFilterDialog v-model="orgFilter" v-model:selected="selectedEntities" />
  </VNavigationDrawer>
</template>
<script lang="ts" setup>
import { beneficiaryApi } from '@/api/beneficiary'
import type { OperationFilter } from '@/api/operation'
import { operationsGroupApi } from '@/api/operationsGroup'
import { territoryApi } from '@/api/territory'
import { userApi } from '@/api/user'
import RemoteAutoComplete from '@/components/RemoteAutoComplete.vue'
import { usePeriodsStore } from '@/stores/periods'
import { useStepsStore } from '@/stores/steps'
import { useUserStore } from '@/stores/user'
import { type Beneficiary } from '@/types/beneficiary'
import {
  controlOrderNatureLabel,
  type ControlOrderNature,
  type StandardizedOperationSheet,
} from '@/types/calcul/standardizedOperationSheet'
import { type EmmyFolder } from '@/types/emmyFolder'
import type { Entity } from '@/types/entity'
import { mapToReadableStatus, operationStatus, type OperationStatus } from '@/types/operation'
import type { OperationsGroup } from '@/types/operationsGroup'
import type { Page, Pageable } from '@/types/pagination'
import { commercialStatusTitle, type Step } from '@/types/steps'
import { formatTerritory } from '@/types/territory'
import { displayFullnameUser, type User } from '@/types/user'
import type { ValuationType } from '@/types/valuation'
import { clone, cloneDeep, isEqual } from 'lodash'
import type { PropType } from 'vue'
import { VCheckbox, VCheckboxBtn, VListItem, VTextField } from 'vuetify/components'
import EmmyFolderFilterDialog from './dialog/EmmyFolderFilterDialog.vue'
import EntityFilterDialog from './dialog/EntityFilterDialog.vue'
import StandardizedOperationSheetFilterDialog from './dialog/StandardizedOperationSheetFilterDialog.vue'
import { add, format } from 'date-fns'
import {
  NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY,
  NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION,
} from '@/types/osh/oshFolder'

const props = defineProps({
  originalFilter: {
    type: Object as PropType<OperationFilter>,
    default: () => {},
  },
  valuationMode: Boolean,
  forSimulation: Boolean,
  mode: {
    type: String as PropType<'default' | 'osh'>,
    default: 'default',
  },
})

const emit = defineEmits<{
  'update:original-filter': [filter: OperationFilter]
  'update:valuation-mode': [isActive: boolean]
  'update:model-value': [boolean]
}>()

const fosFilter = ref(false)
const orgFilter = ref(false)
const filter = ref(cloneDeep(props.originalFilter))
const checkingValuation = ref(clone(props.valuationMode))
const selectedBeneficiaries = ref<Beneficiary[]>([])
const selectedOperationsGroup = ref<OperationsGroup[]>([])
const userStore = useUserStore()

const yesNoOptions = ref([
  {
    title: 'OUI',
    value: true,
  },
  {
    title: 'NON',
    value: false,
  },
])

const selectedInstructors = ref<User[]>([])

const periodsStore = usePeriodsStore()
const valuationTypes = ref(emptyValue<Page<ValuationType>>())

const selectedEntities = ref<Entity[]>([])
const selectedFos = ref<StandardizedOperationSheet[]>([])

const stepsStore = useStepsStore()

const steps = computed((): Step[] => {
  return stepsStore.steps!.filter((s) => s.id != 10)
})

const load = async () => {
  await handleAxiosPromise(valuationTypes, valuationTypeApi.getAll({ displayActive: false }))
  initFilter()
}

const initFilter = async () => {
  // console.debug('initFilter')
  if ((props.originalFilter.beneficiaryIds?.length ?? 0) > 0) {
    beneficiaryApi
      .findAll({}, { ids: props.originalFilter.beneficiaryIds })
      .then((response) => (selectedBeneficiaries.value = response.data.content))
  } else {
    selectedBeneficiaries.value = []
  }

  if ((props.originalFilter.instructorIds?.length ?? 0) > 0) {
    userApi
      .getAll({}, { ids: props.originalFilter?.instructorIds })
      .then((response) => (selectedInstructors.value = response.data.content))
  } else {
    selectedInstructors.value = []
  }

  if ((props.originalFilter.operationsGroupIds?.length ?? 0) > 0) {
    operationsGroupApi
      .findAll({ ids: props.originalFilter.operationsGroupIds }, {})
      .then((response) => (selectedOperationsGroup.value = response.data.content))
  } else {
    selectedOperationsGroup.value = []
  }

  if ((props.originalFilter.standardizedOperationSheetIds?.length ?? 0) > 0) {
    standardizedOperationSheetApi
      .findAll({}, { ids: props.originalFilter.standardizedOperationSheetIds })
      .then((response) => (selectedFos.value = response.data.content))
  } else {
    selectedFos.value = []
  }

  if ((props.originalFilter.entityNavFullIds?.length ?? 0) > 0) {
    const ids = props.originalFilter.entityNavFullIds?.map((navFullId) =>
      navFullId.substring(navFullId.length - 3, navFullId.length)
    )
    entityApi
      .getAll(
        {
          ids: ids,
        },
        {}
      )
      .then((response) => (selectedEntities.value = response.data.content))
  } else {
    selectedEntities.value = []
  }

  if ((props.originalFilter.emmyFolderIds?.length ?? 0) > 0) {
    emmyFolderApi
      .findAll({ ids: props.originalFilter.emmyFolderIds }, {})
      .then((response) => (selectedEmmyFolder.value = response.data.content))
  } else {
    selectedEmmyFolder.value = []
  }

  if (props.originalFilter.noControlOrder) {
    controlOrderNatures.value = ['NONE']
  } else {
    controlOrderNatures.value = props.originalFilter.controlOrderNatures?.concat() ?? []
  }
}

const updateStepIds = (stepId: number) => {
  if (filter.value.stepIds && filter.value.stepIds.includes(stepId)) {
    filter.value.stepIds!.splice(
      filter.value.stepIds!.findIndex((id) => id === stepId),
      1
    )
  } else if (filter.value.stepIds) {
    filter.value.stepIds.push(stepId)
    filter.value.stepIds.sort()
  } else {
    filter.value.stepIds = [stepId]
  }
}

const updateCommercialStatuses = (status: OperationStatus) => {
  if (filter.value.operationStatuses!.includes(status)) {
    filter.value.operationStatuses!.splice(
      filter.value.operationStatuses!.findIndex((id) => id === status),
      1
    )
  } else {
    filter.value.operationStatuses!.push(status)
    filter.value.operationStatuses!.sort()
  }
}

watch(selectedBeneficiaries, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    if (v && v.length > 0) {
      filter.value.beneficiaryIds = v.map((b) => b.id)
    } else {
      filter.value.beneficiaryIds = []
    }
  }
})

watch(selectedInstructors, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    if (v && v.length > 0) {
      filter.value.instructorIds = v.map((b) => b.id)
    } else {
      filter.value.instructorIds = []
    }
  }
})

watch(selectedOperationsGroup, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    if (v && v.length > 0) {
      filter.value.operationsGroupIds = v.map((b) => b.id)
    } else {
      filter.value.operationsGroupIds = []
    }
  }
})

watch(
  () => props.originalFilter,
  (v) => {
    // console.debug('watch props.originalFilter', v)
    if (v && !isEqual(v, filter.value)) {
      // console.debug('updated props.originalFilter')
      filter.value = cloneDeep(v)
      if (props.originalFilter.beneficiaryIds === undefined) {
        selectedBeneficiaries.value = []
      }
      if (props.originalFilter.operationsGroupIds === undefined) {
        selectedOperationsGroup.value = []
      }
      if (props.originalFilter.standardizedOperationSheetIds === undefined) {
        selectedFos.value = []
      }
      if (props.originalFilter.entityNavFullIds === undefined) {
        selectedEntities.value = []
      }
      if (props.originalFilter.emmyFolderIds === undefined) {
        selectedEmmyFolder.value = []
      }
      initFilter()
    }
  },
  {
    deep: true,
  }
)

watch(
  () => props.valuationMode,
  (v) => (checkingValuation.value = clone(v))
)

watch(selectedEntities, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    filter.value.entityNavFullIds = v.map((i) => i.navFullId)
  }
})

const removeEntity = (org: Entity) => {
  selectedEntities.value = selectedEntities.value.filter((item) => item.id != org.id)
}

watch(selectedFos, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    filter.value.standardizedOperationSheetIds = v.map((i) => i.id)
  }
})
const removeFos = (fos: StandardizedOperationSheet) => {
  selectedFos.value = selectedFos.value.filter((item) => item.id != fos.id)
}

watch(
  filter,
  (v) => {
    if (v) {
      emit('update:original-filter', filter.value)
    }
  },
  {
    deep: true,
  }
)

watch(checkingValuation, (v) => emit('update:valuation-mode', v))

const emmyFolderDialog = ref(false)
const selectedEmmyFolder = ref<EmmyFolder[]>([])
watch(selectedEmmyFolder, (v, oldV) => {
  if (!isEqual(v, oldV)) {
    filter.value.emmyFolderIds = v.map((i) => i.id)
  }
})
const removeEmmyFolder = (fos: EmmyFolder) => {
  selectedEmmyFolder.value = selectedEmmyFolder.value.filter((item) => item.id != fos.id)
}

onMounted(load)

const instructionDelayFilerItems = [
  {
    title: '3 mois',
    value: '3 mois',
    //
  },
  {
    title: 'PNCEE dépassé',
    value: 'PNCEE dépassé',
    // value: format(add(new Date(), { days: -NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION }), localDateFormat),
  },
] as const

const updateInstructionDelay = (v: (typeof instructionDelayFilerItems)[number]['value']) => {
  filter.value.instructionDelay = v
  if (v === '3 mois') {
    filter.value.maxEndWorksDate = format(
      add(new Date(), { days: -NUMBER_OF_DAY_AFTER_EFFECTIVE_END_WORKS_FOR_INSTRUCTION_DELAY }),
      localDateFormat
    )
    filter.value.minEndWorksDate = format(
      add(new Date(), { days: -NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION }),
      localDateFormat
    )
  } else if (v === 'PNCEE dépassé') {
    filter.value.maxEndWorksDate = format(
      add(new Date(), { days: -NUMBER_OF_DAY_BEFORE_PNCEE_EXPIRATION }),
      localDateFormat
    )
    filter.value.minEndWorksDate = ''
  } else {
    filter.value.maxEndWorksDate = ''
    filter.value.minEndWorksDate = ''
  }
}

const updateControlOrderNatures = (ev: (ControlOrderNature | 'NONE')[]) => {
  if (ev.includes('NONE') && controlOrderNatures.value.includes('NONE') !== true) {
    controlOrderNatures.value = ['NONE']
    // filter.value.noControlOrder = true
  } else {
    // filter.value.noControlOrder = undefined
    controlOrderNatures.value = ev.filter((it) => it !== 'NONE')
  }

  filter.value.noControlOrder = controlOrderNatures.value.includes('NONE')
  filter.value.controlOrderNatures = controlOrderNatures.value.filter((it) => it !== 'NONE') as ControlOrderNature[]
}

const controlOrderNatures = ref<(ControlOrderNature | 'NONE')[]>([])
const controlOrderNatureItems = (
  controlOrderNatureLabel as unknown as { value: ControlOrderNature | 'NONE'; label: string }[]
)
  .concat({
    value: 'NONE',
    label: 'Non',
  })
  .map((i) => ({
    title: i.label,
    value: i.value,
  }))
</script>
