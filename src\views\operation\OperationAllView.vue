<template>
  <NjPage
    title="Liste des opérations CEE"
    :loading="data.loading"
    :error-message="data.error"
    :expend-body="arrayExpanded"
  >
    <template #header-actions>
      <DoublonDialog />
      <NjBtn :to="{ name: 'OperationOneNewView' }" prepend-icon="mdi-pencil"> Créer une opération </NjBtn>
    </template>
    <template v-if="hasFeatureTdb" #sub-header>
      <VRow class="flex-column" dense>
        <VCol>
          <VRow>
            <VCol>
              <VCheckbox v-model="filter.myRequests" label="Mes opérations" class="flex-grow-0" />
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="align-center" dense>
            <VCol cols="3">
              <SearchInput
                v-model:loading="data.loading"
                :model-value="pageFilter.search"
                clearable
                @update:model-value="updateSearch"
              />
            </VCol>
            <!-- <VCol class="subheader-actions align-center flex-grow-0" style="min-width: fit-content">

              <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
                Réinitialiser les filtres
              </VLink>
            </VCol>
            <VCol>
              <ButtonsMenuAdapter :buttons="headerButtons" />
            </VCol> -->
            <VCol cols="2">
              <VTextField
                label="Organisations"
                append-inner-icon="mdi-format-list-bulleted"
                readonly
                class="v-field--one-line-chip bg-white"
                clearable
                :active="!!selectedEntities.length"
              >
                <VChip v-if="selectedEntities[0]" density="compact" class="flex-grow-1 flex-basis-0"
                  >{{ selectedEntities[0].name }} ({{ selectedEntities[0].id }})</VChip
                >
                <VChip v-if="selectedEntities.length > 1" density="compact" class="px-2">
                  + {{ selectedEntities.length - 1 }}</VChip
                >
                <EntityFilterDialog
                  :selected="selectedEntities"
                  activator="parent"
                  @update:selected="updateSelectedEntities"
                />
              </VTextField>
            </VCol>
            <VCol cols="2">
              <RemoteAutoComplete
                v-model="filter.beneficiaryIds"
                label="Bénéficiaires"
                :query-for-one="(id) => beneficiaryApi.findOne(id)"
                :query-for-ones="(ids) => beneficiaryApi.findAll({ size: 1000, sort: ['displayName'] }, { ids })"
                :query-for-all="
                  (s, pageable) => beneficiaryApi.findAll({ ...pageable, sort: ['displayName'] }, { search: s })
                "
                :item-title="(item) => item.displayName ?? ''"
                chips
                closable-chips
                multiple
                infinite-scroll
                clearable
                :max-elements="1"
              />
            </VCol>
            <VCol cols="2">
              <RemoteAutoComplete
                v-model="filter.propertyTeamEntityNavFullIds"
                label="Equipes"
                class="v-field--one-line-chip"
                :query-for-one="(id) => gtfOrgsApi.getOneEntity(id.substring(id.length - 3))"
                :query-for-all="
                  (s, pageable) =>
                    gtfOrgsApi
                      .findAllEntities({
                        q: {
                          ...(s ? { name: { $instr: s.trim() }, id: { $instr: s.trim() } } : {}),
                          enabled: { $eq: 1 },
                          nav_level: 6,
                          nav_full_id: {
                            $or: userStore.currentUser.entities.map((it) => ({ $instr: it.navFullId.trim() })),
                          },
                          $orderby: { nav_full_id: 'ASC' },
                        },
                        limit: 20,
                        offset: 20 * (pageable?.page ?? 0),
                      })
                      .then((it) => ({ ...it, data: mapGtfPagToPage(it.data) }))
                "
                :item-title="(it) => it.name + ' (' + it.id + ')'"
                item-value="nav_full_id"
                infinite-scroll
                clearable
              />
            </VCol>
            <VCol cols="2">
              <VSelect
                v-model="filter.periodIds"
                label="Périodes CEE"
                class="bg-white"
                :items="periodsStore.periods"
                item-title="name"
                item-value="id"
                multiple
                clearable
              />
            </VCol>

            <CardDialog v-model="changeEntityDialog" title="Changer d'organisation" width="650px">
              <VRow class="flex-column">
                <VCol>
                  <ErrorAlert type="warning" :message="changeEntityMessage" />
                </VCol>
                <VCol>
                  <VForm>
                    <RemoteAutoComplete
                      v-model="selectedEntity"
                      label="Organisation"
                      :query-for-one="(id) => entityApi.getOne(id)"
                      :query-for-all="
                        (s, pageable) => entityApi.getAll({ search: s, visible: true, level: 4 }, pageable)
                      "
                      item-title="name"
                      item-value="id"
                      :rules="[requiredRule]"
                      infinite-scroll
                    />
                  </VForm>
                </VCol>
              </VRow>
              <template #actions>
                <NjBtn variant="outlined" @click="changeEntityDialog = false">Annuler</NjBtn>
                <NjBtn :loading="changeEntityLoading" @click="changeEntity">Valider</NjBtn>
              </template>
            </CardDialog>

            <VCol cols="1">
              <NjBtn variant="outlined" prepend-icon="mdi-filter-variant" @click="handleFilterDrawer"> Filtres </NjBtn>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
    <template v-else #sub-header>
      <VRow class="align-center">
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VCol class="subheader-actions align-center flex-grow-0" style="min-width: fit-content">
          <VCheckbox v-model="filter.myRequests" label="Mes opérations" class="flex-grow-0" />
          <NjBtn @click="handleFilterDrawer"> Filtres </NjBtn>
          <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
            Réinitialiser les filtres
          </VLink>
        </VCol>
        <VCol>
          <ButtonsMenuAdapter :buttons="headerButtons" />
        </VCol>

        <CardDialog v-model="changeEntityDialog" title="Changer d'organisation" width="650px">
          <VRow class="flex-column">
            <VCol>
              <ErrorAlert type="warning" :message="changeEntityMessage" />
            </VCol>
            <VCol>
              <VForm>
                <RemoteAutoComplete
                  v-model="selectedEntity"
                  label="Organisation"
                  :query-for-one="(id) => entityApi.getOne(id)"
                  :query-for-all="(s, pageable) => entityApi.getAll({ search: s, visible: true, level: 4 }, pageable)"
                  item-title="name"
                  item-value="id"
                  :rules="[requiredRule]"
                  infinite-scroll
                />
              </VForm>
            </VCol>
          </VRow>
          <template #actions>
            <NjBtn variant="outlined" @click="changeEntityDialog = false">Annuler</NjBtn>
            <NjBtn :loading="changeEntityLoading" @click="changeEntity">Valider</NjBtn>
          </template>
        </CardDialog>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100 flex-column">
        <VCol v-if="hasFeatureTdb" v-show="!arrayExpanded">
          <VRow>
            <VCol cols="6">
              <div><VIcon icon="mdi-poll" size="16" class="me-1" color="#60798B"></VIcon>Vue d'ensemble</div>
              <VDivider class="mt-1 mb-2"></VDivider>
              <VRow dense class="flex-column">
                <VCol>
                  <VRow dense>
                    <VCol cols="6">
                      <NumberCardDashboardComponent
                        title="Opérations en cours de l'étape 20 à 50"
                        :disabled="
                          !isEqual(filter.stepIds, []) ||
                          (!isEqual(filter.operationStatuses, []) && !isEqual(filter.operationStatuses, ['DOING']))
                        "
                        :value="
                          (f) =>
                            operationApi
                              .getSummary({ ...f, stepIds: [20, 30, 40, 50], operationStatuses: ['DOING'] })
                              .then((it) => it.data.operationsNumber)
                        "
                      >
                        <template #after-value>
                          <DashboardFilterButton
                            :filter="{ stepIds: [20, 30, 40, 50], operationStatuses: ['DOING'] }"
                            disabled-message="Incompatible avec le filtre sur les étapes et/ou le processus"
                          />
                        </template>
                      </NumberCardDashboardComponent>
                    </VCol>
                    <VCol cols="6">
                      <NumberCardDashboardComponent
                        title="Regroupements"
                        :disabled="
                          !!filter.operationsGroupId ||
                          !!filter.operationsGroupIds?.length ||
                          (!isEqual(filter.operationStatuses, []) && !isEqual(filter.operationStatuses, ['DOING']))
                        "
                        :value="
                          (v) =>
                            operationsGroupApi
                              .findAll(
                                {
                                  beneficiaryIds: v.beneficiaryIds,
                                  entityNavFullIds: v.entityNavFullIds,
                                  periodIds: v.periodIds,
                                  search: v.search,
                                },
                                { size: 0 }
                              )
                              .then((it) => it.data.totalElements)
                        "
                      >
                        <template #after-value>
                          <DashboardFilterButton
                            :filter="{ inOperationsGroup: true }"
                            disabled-message="Incompatible avec le filtre sur les regroupements"
                          />
                        </template>
                      </NumberCardDashboardComponent>
                    </VCol>
                  </VRow>
                </VCol>
                <VCol>
                  <NumberCardDashboardComponent
                    title="Volume d'opérations en cours par étape"
                    :value="generatorQuantityData"
                    :selected="hasStepFilter"
                  >
                    <template #after-title>
                      <!-- <VCheckboxBtn v-model="noSplitData" /> -->
                      <VBtnToggle
                        v-model="barChartType"
                        density="compact"
                        variant="outlined"
                        mandatory
                        border="md"
                        color="primary"
                      >
                        <VBtn value="quantity"> Quantité </VBtn>
                        <VBtn value="kwhc"> kWhc </VBtn>
                      </VBtnToggle>
                    </template>
                    <template #value="{ value, loading }">
                      <div :class="{ 'v-hidden': !hasStepFilter }" class="d-flex justify-end mt-2">
                        <VBtn
                          color="primary"
                          variant="text"
                          density="compact"
                          class="rounded-0"
                          prepend-icon="mdi-close"
                          @click="filter.stepIds = []"
                          >Effacer les filtres</VBtn
                        >
                      </div>
                      <VChart
                        :loading="loading && globalLoading"
                        class="chart"
                        :option="generateBarChartsOptions(value, noSplitData)"
                        style="height: 340px"
                        autoresize
                        @click="clickBarChartElement"
                      />
                    </template>
                  </NumberCardDashboardComponent>
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="4">
              <div>
                <VIcon icon="mdi-clock-outline" size="16" class="me-1" color="#DB3735"></VIcon>Fin de travaux
                prévisionnelle
              </div>
              <VDivider class="mt-1 mb-2"></VDivider>
              <VRow dense class="flex-column">
                <VCol>
                  <NumberCardDashboardComponent
                    title="Opérations avec fin de travaux dans 1 mois"
                    subtitle="Dates prévisionnelles des opérations à l'étape 50"
                    tooltip="Nombre d'opération à l'étape 50 dont les dates prévisionnelles de fin de travaux approchent (30 prochains jours)"
                    :value="
                      (v) =>
                        operationApi
                          .getSummary({
                            ...v,
                            minEstimatedEndWorksDate: formatLocalDate(new Date()),
                            maxEstimatedEndWorksDate: formatLocalDate(addMonths(new Date(), 1)),
                            operationStatuses: ['DOING'],
                            stepIds: [50],
                          })
                          .then((it) => it.data.operationsNumber)
                    "
                    :disabled="
                      !!filter.minEndWorksDate ||
                      !!filter.maxEndWorksDate ||
                      !!filter.minEstimatedEndWorksDate ||
                      !!filter.maxEstimatedEndWorksDate ||
                      !!filter.minActualEndWorksDate ||
                      !!filter.maxActualEndWorksDate ||
                      (!isEqual(filter.stepIds, []) && !isEqual(filter.stepIds, [50])) ||
                      (!isEqual(filter.operationStatuses, []) && !isEqual(filter.operationStatuses, ['DOING']))
                    "
                  >
                    <template #after-value>
                      <DashboardFilterButton
                        :filter="{
                          minEstimatedEndWorksDate: formatLocalDate(new Date()),
                          maxEstimatedEndWorksDate: formatLocalDate(addMonths(new Date(), 1)),
                          operationStatuses: ['DOING'],
                          stepIds: [50],
                        }"
                        disabled-message="Incompatible avec le filtre sur les dates de fin de travaux, le processus et/ou les étapes"
                      />
                    </template>
                  </NumberCardDashboardComponent>
                </VCol>
                <VCol>
                  <NumberCardDashboardComponent
                    title="Fin de travaux dépassée depuis au moins 3 mois"
                    subtitle="Dates prévisionnelles des opérations à l'étape 50"
                    tooltip="Nombre d'opérations à l'étape 50 dont les dates prévisionnelles de fin de travaux sont dépassées depuis 3 mois ou plus"
                    :value="generatorDelayedEstimatedEndWorksData"
                    :selected="hasDelayedWorksFilter"
                    :disabled="isDelayedWorksDisabled"
                  >
                    <template #value="{ value, loading }">
                      <div class="text-end">
                        <DashboardFilterButton
                          v-if="!hasDelayedWorksFilter"
                          :filter="{
                            maxEstimatedEndWorksDate: formatLocalDate(add(new Date(), { months: -3 })),
                            stepIds: [50],
                            operationStatuses: ['DOING'],
                          }"
                          disabled-message="Incompatible avec le filtre sur les dates de fin de travaux, le processus et/ou les étapes"
                          label="Filtrer sur le total"
                        />
                        <VBtn
                          v-else
                          color="primary"
                          variant="text"
                          density="compact"
                          class="rounded-0"
                          prepend-icon="mdi-close"
                          @click="
                            ((filter.minEstimatedEndWorksDate = undefined),
                            (filter.maxEstimatedEndWorksDate = undefined))
                          "
                          >Effacer</VBtn
                        >
                      </div>
                      <VChart
                        :loading="loading && globalLoading"
                        :option="generateDoughnutsOptions(value, isDelayedWorksDisabled && !hasDelayedWorksFilter)"
                        style="height: 328px"
                        autoresize
                        @selectchanged="onDoughnutsSelectionChanged"
                      />
                    </template>
                  </NumberCardDashboardComponent>
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="2" class="d-flex flex-column">
              <div><VIcon icon="mdi-bell" size="16" class="me-1" color="primary"></VIcon>Actions clés</div>
              <VDivider class="mt-1 mb-2"></VDivider>
              <div class="flex-grow-1 d-flex flex-column gap-2">
                <NumberCardDashboardComponent
                  class="flex-grow-1 flex-basis-0 pa-4"
                  title="Simulations à traiter"
                  tooltip="Simulations indiquées comme « A traiter » par leur créateur, à transformer en opérations"
                  :disabled="
                    !!filter.stepIds?.length ||
                    (!isEqual(filter.operationStatuses, []) && !isEqual(filter.operationStatuses, ['DOING']))
                  "
                  :targeted-value="0"
                  :value="
                    (v) =>
                      operationApi
                        .getSummary({ ...v, stepIds: [0], toProcess: true, operationStatuses: ['DOING'] })
                        .then((it) => it.data.operationsNumber)
                  "
                >
                  <template #after-value>
                    <VBtn
                      no-padding
                      prepend-icon="mdi-open-in-new"
                      variant="text"
                      color="primary"
                      density="compact"
                      target="_blank"
                      :to="{
                        name: 'SimulationAllView',
                        query: { ...(filter as any), operationStatuses: ['DOING'], toProcess: 'true' },
                      }"
                      >Ouvrir la liste</VBtn
                    >
                  </template>
                </NumberCardDashboardComponent>
                <NumberCardDashboardComponent
                  class="flex-grow-1 flex-basis-0 pa-4"
                  title="VF à transmettre"
                  tooltip="Opérations pour lesquelles une version finale est disponible et à transmettre aux clients (à l'étape 100, ou à l'étape 70 en cas de contrôle)"
                  :targeted-value="0"
                  :disabled="!filter.stepIds?.every((it) => it >= 70)"
                  :value="
                    (v) =>
                      operationApi
                        .getSummary({ ...v, mustSendFinalVersion: true })
                        .then((it) => it.data.operationsNumber)
                  "
                >
                  <template #after-value>
                    <DashboardFilterButton :filter="{ mustSendFinalVersion: true }" />
                  </template>
                </NumberCardDashboardComponent>
                <NumberCardDashboardComponent
                  class="flex-grow-1 flex-basis-0 pa-4"
                  title="SAV à programmer"
                  tooltip="Documents du SAV à transmettre aux opérationnels (équipe travaux) pour remise en conformité du chantier et renvoi du SAV signé par le client à la cellule Contrôle National du SAV + justificatifs éventuels"
                  :targeted-value="0"
                  :disabled="!filter.stepIds?.every((it) => it >= 70)"
                  :value="
                    (v) =>
                      operationApi
                        .getSummary({ ...v, afterSalesServiceStatuses: ['SENT_BY_CONTROL_OFFICE'] })
                        .then((it) => it.data.operationsNumber)
                  "
                >
                  <template #after-value>
                    <DashboardFilterButton :filter="{ afterSalesServiceStatuses: ['SENT_BY_CONTROL_OFFICE'] }" />
                  </template>
                </NumberCardDashboardComponent>
                <!-- Temporaire le temps que les 2 autres cases se remplissent -->
                <VSpacer />
                <VSpacer />
                <!-- <VCol>
                  <NumberCardDashboardComponent
                    title="VF à transmettre"
                    tooltip="Opérations pour lequelles une version finale est disponible et à transemttre aux clients (à l'étape 100, ou à l'étape 70 en cas de contrôle)"
                    :value="
                      (v) =>
                        operationApi
                          .getSummary({
                            ...v,
                            finalVersionToSend: true,
                            periodIds:
                              v.periodIds ?? periodsStore.periods!.filter((it) => it.active).map((it) => it.id),
                          })
                          .then((it) => it.data.operationsNumber)
                    "
                  />
                </VCol> -->
                <!-- <VCol>
                  <NumberCardDashboardComponent title="Commandes à passer" :value="59" />
                </VCol>
                <VCol>
                  <NumberCardDashboardComponent title="SAV à traiter" :value="59" />
                </VCol> -->
              </div>
            </VCol>
          </VRow>
        </VCol>
        <VCol class="d-flex flex-column">
          <VSheet v-if="hasFeatureTdb" class="d-flex align-center" border>
            <div class="px-4 py-2">{{ data.value?.totalElements }} opérations</div>
            <VSpacer />
            <template v-if="userStore.hasRole('ADMIN_PLUS')">
              <VBtn color="primary" variant="text" @click="batchUpdateDialog = true"> MAJ en masse </VBtn>
              <VBtn color="primary" variant="text" :disabled="disableChangeEntity" @click="changeEntityDialog = true">
                Changer d'organisation
              </VBtn>
              <VBtn color="primary" variant="text" :loading="exportOperationsLoading" @click="purgeOperations">
                Purger les opérations
              </VBtn>
            </template>
            <VBtn color="primary" variant="text" :loading="exportOperationsLoading" @click="exportOperations"
              >Exporter</VBtn
            >
            <VBtn color="primary" variant="text" @click="handleColumnManager">Personnaliser</VBtn>
            <VBtn
              :icon="arrayExpanded ? 'mdi-arrow-collapse' : 'mdi-arrow-expand'"
              variant="text"
              color="primary"
              rounded="0"
              @click="arrayExpanded = !arrayExpanded"
            ></VBtn>
          </VSheet>
          <div class="flex-grow-1 flex-basis-0">
            <OperationTable
              v-model:selection="selection"
              v-model:drawer="drawer"
              :pageable="pageable"
              :update-pageable="updatePageable"
              :data="data.value!"
              :valuations="filter.valuationMode ? valuations : undefined"
              :fixed="arrayExpanded"
              @update:drawer="(event, id) => (id ? getOperationDetail(id) : undefined)"
            />
          </div>
        </VCol>
        <FilterDrawer
          v-model:original-filter="filter"
          v-model:valuation-mode="filter.valuationMode"
          :model-value="drawer === 'filter'"
          :count="filterCount"
          @update:model-value="drawer = $event ? 'filter' : undefined"
        />
      </VRow>

      <CardDialog v-model="batchUpdateDialog">
        <template #title> Modification en masse des données </template>

        <template #default>
          <VRow class="flex-column">
            <VCol>
              <VAlert type="warning">Ne modifiez en masse que si vous êtes sur de ce que vous faites !</VAlert>
            </VCol>
            <VCol v-if="selection.length > 0">
              <VAlert type="warning"
                >Vous allez modifié que les éléments sélectionnées (soit un total de
                {{ selection.length }} opérations)</VAlert
              >
            </VCol>
            <VCol>
              <NjDisplayValue label="Valorisation">
                <template #value>
                  <div class="w-50">
                    <VSelect
                      v-model="batchUpdateRequest.valuationTypeId"
                      label="Type"
                      :items="valuationTypesStore.activeValuationTypes"
                      item-title="name"
                      item-value="id"
                      clearable
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
          </VRow>
        </template>

        <template #actions="{}">
          <div v-if="batchUpdating">
            Opérations traités {{ batchUpdateCurrentIndex }}/{{ data.value?.totalElements }}
          </div>
          <NjBtn :loading="batchUpdating" @click="batchUpdate">Valider</NjBtn>
        </template>
      </CardDialog>
      <AlertDialog
        v-model="batchUpdateResultDialog"
        :negative-button="false"
        @click:positive="batchUpdateResultDialog = false"
      >
        <VAlert v-if="batchUpdatingError" type="error">{{ batchUpdatingError }}</VAlert>
        <div>
          Opérations mises à jour ({{ batchUpdateResults.length }} opération(s) mises à jour)
          <template v-if="batchUpdateErrorOperationIds.length > 0">
            ({{ batchUpdateErrorOperationIds.length }} opérations en erreur)</template
          >:
        </div>
        <ul>
          <li v-for="r in batchUpdateResults" :key="r[0]">
            <RouterLink :to="{ name: 'OperationOneView', params: { id: r[0] } }" targer="_blank">{{ r[1] }}</RouterLink>
          </li>
        </ul>

        <template v-if="batchUpdateErrorOperationIds.length > 0">
          <p>Opérations en erreur :</p>
          <ul>
            <li v-for="r in batchUpdateErrorOperationIds" :key="r[0]">
              <RouterLink :to="{ name: 'OperationOneView', params: { id: r[0] } }" targer="_blank"
                >{{ r[1] }}(étape {{ r[2] }})</RouterLink
              >
            </li>
          </ul>
        </template>
      </AlertDialog>

      <VScrollYReverseTransition v-if="hasFeatureTdb">
        <VSheet
          v-if="!!filterCount"
          :timeout="-1"
          color="#E7EEFC"
          type="info"
          elevation="4"
          style="position: absolute; bottom: 16px; left: 50%; transform: translateX(-50%); padding: 4px 8px 4px 24px"
        >
          <VBadge :model-value="!!filterCount" :content="filterCount" color="primary" inline></VBadge> filtres appliqués
          <NjBtn variant="text" prepend-icon="mdi-refresh" @click="filter = defaultFilter"
            >Réinitialiser les filtres</NjBtn
          >
        </VSheet>
      </VScrollYReverseTransition>
    </template>
    <template #drawer>
      <OperationDrawer
        ref="operationDrawerRef"
        :model-value="drawer === 'detail'"
        :operation="selectedOperation.value"
        :loading="selectedOperation.loading"
        @update:model-value="drawer = $event ? 'detail' : undefined"
        @update:operation="onUpdateOperation"
        @final-version-sent="reloadData"
      />
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { beneficiaryApi } from '@/api/beneficiary'
import { entityApi } from '@/api/entity'
import { gtfOrgsApi } from '@/api/external/gtf/gtfOrgs'
import { mapGtfPagToPage } from '@/api/external/gtf/type'
import { operationApi, type OperationFilter } from '@/api/operation'
import { operationsGroupApi } from '@/api/operationsGroup'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import { useDialogStore } from '@/stores/dialog'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useStepsStore } from '@/stores/steps'
import { useUserStore } from '@/stores/user'
import { useValuationTypesStore } from '@/stores/valuationTypes'
import { formatLocalDate } from '@/types/date'
import type { Entity } from '@/types/entity'
import { type Operation, type OperationRequest } from '@/types/operation'
import type { OperationSummary } from '@/types/operationSummary'
import { usePaginationInQuery } from '@/types/pagination'
import { requiredRule } from '@/types/rule'
import { userHasRole } from '@/types/user'
import type { Valuation } from '@/types/valuation'
import type { AxiosPromise } from 'axios'
import { add, addMonths } from 'date-fns'
import { BarChart, PieChart } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsOption } from 'echarts/types/dist/shared'
import { cloneDeep, debounce, isArray, isBoolean, isEmpty, isEqual, isMatch, omit, omitBy } from 'lodash'
import VChart from 'vue-echarts'
import type { LocationQuery } from 'vue-router'
import { dashboardDataKey } from '../dashboard/keys'
import NumberCardDashboardComponent from '../dashboard/NumberCardDashboardComponent.vue'
import DoublonDialog from '../DoublonDialog.vue'
import { useExportOperation } from '../exportOperation'
import DashboardFilterButton from './DashboardFilterButton.vue'
import EntityFilterDialog from './dialog/EntityFilterDialog.vue'
import FilterDrawer from './FilterDrawer.vue'
import OperationDrawer from './OperationDrawer.vue'
import OperationTable from './OperationTable.vue'

const stepStore = useStepsStore()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const valuationTypesStore = useValuationTypesStore()
const periodsStore = usePeriodsStore()
const selection = ref<Operation[]>([])

const defaultFilter = {
  ...makeEmptyFilter(),
  operationStatuses: ['DOING'],
} as OperationFilter

const filter = ref<OperationFilter>(cloneDeep(defaultFilter))
const defaultOrder = userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR')
  ? ['statusRank,ASC', 'lastValidatedStepDateTime,DESC']
  : ['statusRank,ASC', 'chronoCode,DESC']

/**
 * Part Dashboard - START
 */
const noSplitData = ref(false)
const globalLoading = ref(false)
const dashboardFilter = ref<OperationFilter>({})
provide(dashboardDataKey, {
  filter,
  loading: globalLoading,
  dashboardFilter,
})

const valuations = ref<Valuation[][]>([[]])
use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  BarChart,

  GridComponent,

  GraphicComponent,
])
const generateDoughnutsOptions = (data: number[], isDelayedWorksDisabled: boolean): EChartsOption => {
  const isEmpty = data?.every((it) => it === 0) ?? true
  const onlyOneSection = data?.reduce((acc, v) => acc + (v > 0 ? 1 : 0), 0) === 1
  const newData =
    data && !isEmpty
      ? [
          {
            value: data[0],
            name: 'Depuis 3 mois',
            itemStyle: { color: '#FFC5B9' /* light/red/red-300 */ },
            selected: isMatch(filter.value, delayedLimitsReferenceDates[0]),
          },
          {
            value: data[1],
            name: 'Depuis 8 mois',
            itemStyle: { color: '#F34F46' /* red-500 */ },
            selected: isMatch(filter.value, delayedLimitsReferenceDates[1]),
          },
          {
            value: data[2],
            name: 'Depuis 10 mois',
            itemStyle: { color: '#AA2424' /* light/red/red-700 */ },
            selected: isMatch(
              filter.value,
              omitBy(delayedLimitsReferenceDates[2], (v) => v === undefined)
            ),
          },
        ]
      : [
          {
            value: 0,
            name: 'Aucune donnée',
            itemStyle: { color: '#CCCCCC' /* light/red/red-700 */ },
            select: {
              disabled: true,
            },
          },
        ]
  const total = newData.reduce((acc, v) => acc + (v.value ?? 0), 0)
  return {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: '#333333',
      textStyle: {
        color: '#FFFFFF',
      },
      borderColor: '#333333',
    },
    legend: {
      bottom: '5%',
      left: 'center',
      selectedMode: false,
      value: newData.map((it) => ({
        name: it.name,
        itemStyle: it.itemStyle,
      })),
    } as any,
    series: [
      {
        type: 'pie',
        dimensions: [],
        radius: ['55%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        selectedMode: isDelayedWorksDisabled || isEmpty ? false : 'single',
        selectedOffset: onlyOneSection ? 0 : 10,
        select: {
          itemStyle: {
            borderColor: '#007ACD',
            borderWidth: 4,
          },
        },
        emphasis: {
          // label: {
          //   show: true,
          //   fontSize: 40,
          //   fontWeight: 'bold'
          // }
          label: {},
        },
        labelLine: {
          show: true,
        },
        itemStyle: {
          borderWidth: 4,
        },
        data: newData,
      },
    ],
    graphic: {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: '' + total,
        fill: '#000',
        fontSize: 48,
        fontWeight: 'bold',
      },
    },
  }
}

const provideDelayReferenceDates = () => {
  const date = new Date()
  return [
    {
      minEstimatedEndWorksDate: formatLocalDate(add(date, { months: -8, days: 1 })),
      maxEstimatedEndWorksDate: formatLocalDate(add(date, { months: -3 })),
    },
    {
      minEstimatedEndWorksDate: formatLocalDate(add(date, { months: -10, days: 1 })),
      maxEstimatedEndWorksDate: formatLocalDate(add(date, { months: -8 })),
    },
    {
      minEstimatedEndWorksDate: undefined,
      maxEstimatedEndWorksDate: formatLocalDate(add(date, { months: -10 })),
    },
  ]
}
const delayedLimitsReferenceDates = provideDelayReferenceDates()
const generatorDelayedEstimatedEndWorksData = (v: OperationFilter) => {
  const initialFilter: OperationFilter = {
    ...omit(v, 'minActualEndWorksDate', 'maxActualEndWorksDate', 'minEndWorksDate', 'maxEndWorksDate'),
    stepIds: [50],
    operationStatuses: ['DOING'],
  }
  return Promise.all([
    operationApi
      .getSummary({
        ...initialFilter,
        ...delayedLimitsReferenceDates[0],
      })
      .then((it) => it.data.operationsNumber),
    operationApi
      .getSummary({
        ...initialFilter,
        ...delayedLimitsReferenceDates[1],
      })
      .then((it) => it.data.operationsNumber),
    operationApi
      .getSummary({
        ...initialFilter,
        ...delayedLimitsReferenceDates[2],
      })
      .then((it) => it.data.operationsNumber),
  ])
}
const stepIdsToDisplayInBarchart = [20, 30, 40, 50, 60, 70, 80, 90, 100]
const generatorQuantityData = (v: OperationFilter) => {
  return Promise.all([
    Promise.all(
      stepIdsToDisplayInBarchart.map((it) => {
        return operationApi
          .getSummary({
            ...v,
            stepIds: isArray(it) ? it : [it],
            minEstimatedEndWorksDate: undefined,
            maxEstimatedEndWorksDate: undefined,
          })
          .then((it) => it.data)
      })
    ),
    operationApi
      .getSummary({
        ...v,
        minEstimatedEndWorksDate: undefined,
        maxEstimatedEndWorksDate: formatLocalDate(add(new Date(), { months: -3 })),
        stepIds: [50],
      })
      .then((it) => it.data),
  ])
}
const barChartType = ref<'kwhc' | 'quantity'>('quantity')
const generateBarChartsOptions = (data2: [OperationSummary[], OperationSummary], noSplit: boolean): any => {
  const datadata =
    data2?.[0].map((it) =>
      barChartType.value === 'kwhc' ? it.classicCumacSum + it.precariousnessCumacSum : it.operationsNumber
    ) ?? []

  const siegeData =
    data2?.[0].map((v, index) => ({
      value:
        index >= 4
          ? barChartType.value === 'kwhc'
            ? v.classicCumacSum + v.precariousnessCumacSum
            : v.operationsNumber
          : 0,
      itemStyle: {
        color: '#C0D4F9',
      },
    })) ?? []
  const correspondantData =
    data2?.[0].map((v, index) => ({
      value:
        index < 3
          ? barChartType.value === 'kwhc'
            ? v.classicCumacSum + v.precariousnessCumacSum
            : v.operationsNumber
          : index === 3
            ? barChartType.value === 'kwhc'
              ? v.classicCumacSum +
                v.precariousnessCumacSum -
                data2[1].classicCumacSum -
                data2[1].precariousnessCumacSum
              : v.operationsNumber - data2[1].operationsNumber
            : 0,
      itemStyle: {
        color: '#3D8CDE',
      },
    })) ?? []
  const errorData =
    data2?.[0].map((v, index) => ({
      value:
        index === 3 && data2[1]
          ? barChartType.value === 'kwhc'
            ? data2[1].classicCumacSum + data2[1].precariousnessCumacSum
            : data2[1].operationsNumber
          : 0,
      itemStyle: {
        color: '#F34F46',
      },
    })) ?? []

  const borderData =
    data2?.[0].map((v, index) => ({
      value: (barChartType.value === 'kwhc' ? v.classicCumacSum + v.precariousnessCumacSum : v.operationsNumber) * 1.02,
      itemStyle: {
        borderColor: '#007ACD',
        borderWidth:
          filter.value.stepIds?.length && filter.value.stepIds.includes(stepIdsToDisplayInBarchart[index]) ? 4 : 0,
      },
    })) ?? []

  const series = noSplit
    ? [
        {
          type: 'bar',
          label: {
            show: true,
            position: 'top',
            formatter: (params: any): string => {
              const value = params.data as number
              if (barChartType.value === 'kwhc') {
                if (value >= 1000000) {
                  return formatNaturalNumber(value / 1000000) + ' G'
                } else if (value >= 1000) {
                  return formatNaturalNumber(value / 1000) + ' M'
                }
                return formatNaturalNumber(value) + ' k'
              }
              return formatNaturalNumber(value) ?? ''
            },
          },
          data: datadata,
        },
      ]
    : [
        {
          name: 'Total',
          type: 'bar',
          label: {
            show: false,
          },
          data: borderData,
          // stack: 'total',
          itemStyle: {
            color: '#transparent',
          },
          barGap: '-100%', // Overlap other bars
          emphasis: {
            disabled: true, // Don't trigger tooltip
          },
          tooltip: {
            show: false,
          },
          z: 100,
        },
        {
          name: 'Opérations en cours (côté agence)',
          type: 'bar',
          label: {
            show: false,
          },
          data: correspondantData,
          stack: 'total',
          itemStyle: {
            color: '#3D8CDE',
          },
        },
        {
          name: 'Opérations en cours (côté pôle national)',
          type: 'bar',
          stack: 'total',
          label: {
            show: false,
          },
          data: siegeData,
          itemStyle: {
            color: '#C0D4F9',
          },
        },
        {
          name: 'Fin de travaux dépassée depuis au moins 3 mois',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'top',
            formatter: (params: any): string => {
              const value =
                siegeData[params.dataIndex].value +
                correspondantData[params.dataIndex].value +
                errorData[params.dataIndex].value

              let styleKey = 'default'

              if (filter.value.stepIds?.length) {
                if (filter.value.stepIds.includes(stepIdsToDisplayInBarchart[params.dataIndex])) {
                  styleKey = 'selected'
                }
              }

              let v = ''
              if (barChartType.value === 'kwhc') {
                if (value >= 1000000) {
                  v = formatNaturalNumber(value / 1000000) + ' G'
                } else if (value >= 1000) {
                  v = formatNaturalNumber(value / 1000) + ' M'
                } else {
                  v = formatNaturalNumber(value) + ' k'
                }
              } else {
                v = formatNaturalNumber(value) ?? ''
              }

              return `{${styleKey}|${v}}`
            },
            rich: {
              default: {},
              selected: {
                fontWeight: 'bold',
              },
            },
          },
          data: errorData,
          itemStyle: {
            color: '#F34F46',
          },
        },
      ]
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
        // z: 0,
        // triggerEmphasis: false,
        // shadowStyle: {
        //   color: 'rgba(150, 150, 150, .5)',
        // },
      },
      backgroundColor: '#333333',
      textStyle: {
        color: '#FFFFFF',
      },
      borderWitdh: 0,
      borderColor: '#333333',

      valueFormatter: (params: any) => {
        const value = params as number
        if (barChartType.value === 'kwhc') {
          return formatNumber(value) + ' kWhc'
        }
        return formatNumber(value)!
      },
      confine: true,
      // triggerOn: 'click',
    },
    // axisPointer: {
    //   // triggerOn: 'click',
    //   triggerEmphasis: false,
    // },
    legend: {
      // show: true,
      selectedMode: false,
      bottom: '0%',
      left: 'center',
      padding: 0,
      itemGap: 5,
      data: [
        'Opérations en cours (côté agence)',
        'Opérations en cours (côté pôle national)',
        'Fin de travaux dépassée depuis au moins 3 mois',
      ],
    },
    xAxis: {
      type: 'category',
      data: [
        { value: '20', itemStyle: {} },
        { value: '30', itemStyle: {} },
        { value: '40', itemStyle: {} },
        { value: '50', itemStyle: {} },
        { value: '60', itemStyle: {} },
        { value: '70', itemStyle: {} },
        { value: '80', itemStyle: {} },
        { value: '90', itemStyle: {} },
        { value: '100', itemStyle: {} },
      ],
      // axisTick: {
      // alignWithLabel: false,
      // interval: 0,
      // },
      axisTick: {},
      axisLabel: {
        interval: 0,
        formatter: (value: any) => {
          let styleKey = 'default'
          if (filter.value.stepIds?.length) {
            if (!filter.value.stepIds.includes(parseInt(value))) {
              // styleKey = 'disabled'
            } else {
              styleKey = 'selected'
            }
          }

          return `{${styleKey}|${value}${styleKey === 'selected' ? ' ✖' : ''}}`
        },
        rich: {
          disabled: {
            backgroundColor: '#E7EEFC66',
            borderColor: '#7FADEF66',
            borderWidth: 1,
            padding: 4,
            color: '#00000066',
          },
          default: {
            backgroundColor: '#E7EEFC',
            borderColor: '#7FADEF',
            borderWidth: 1,
            padding: 4,
            color: '#000000',
          },
          selected: {
            backgroundColor: '#E7EEFC',
            borderColor: '#7FADEF',
            borderWidth: 2,
            padding: 4,
            color: '#000000',
            fontWeight: 'bold',
          },
        },
      },
      tooltip: {
        show: true,
        formatter: (v: any) => {
          const stepId = v.value
          return `${stepId}<br />${stepStore.stepsMap[stepId].name}`
        },
        extraCssText: 'text-align:center;',
      },
      triggerEvent: true,
    },
    yAxis: {
      type: 'value',
      name: barChartType.value === 'kwhc' ? 'Whc' : 'Nb. opérations',
      axisLabel: {
        formatter: (params: any) => {
          const value = params
          if (barChartType.value === 'kwhc') {
            if (value >= 1000000) {
              return (value / 1000000).toLocaleString() + ' G'
            } else if (value >= 1000) {
              return (value / 1000).toLocaleString() + ' M'
            }
            return value.toLocaleString() + ' k'
          }
          return value.toLocaleString()
        },
      },
    },
    series,
  }
}

const selectedEntities = ref<Entity[]>([])
const updateSelectedEntities = (entities: Entity[]) => {
  filter.value.entityNavFullIds = entities.map((it) => it.navFullId)
}
watch(
  () => filter.value.entityNavFullIds,
  (v) => {
    if (
      !isEqual(
        v,
        selectedEntities.value.map((it) => it.navFullId)
      )
    ) {
      if (v?.length) {
        const ids = v.map((navFullId) => navFullId.substring(navFullId.length - 3, navFullId.length))
        entityApi
          .getAll(
            {
              ids: ids,
            },
            {}
          )
          .then((response) => (selectedEntities.value = response.data.content))
      } else {
        selectedEntities.value = []
      }
    }
  }
)
const selectedEntity = ref<string>('')
const changeEntityLoading = ref(false)
const changeEntity = () => {
  changeEntityLoading.value = true
  let operationFilter
  if (selection.value.length == 0) {
    operationFilter = filter.value
  } else {
    operationFilter = { operationIds: selection.value.map((item) => item.id) }
  }
  operationApi
    .changeEntity(operationFilter, { entityId: selectedEntity.value })
    .then((response) => {
      snackbarStore.setSuccess(`${response.data} opérations ont changé d'organisation`)
      reload()
      selection.value = []
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      changeEntityDialog.value = false
      changeEntityLoading.value = false
    })
}

watch(dashboardFilter, () => {
  reload()
})

const hasStepFilter = computed(() => {
  return !!filter.value.stepIds?.length
})
const hasDelayedWorksFilter = computed(() => {
  return [
    ...delayedLimitsReferenceDates,
    { maxEstimatedEndWorksDate: formatLocalDate(add(new Date(), { months: -3 })) },
  ].some((it) =>
    isMatch(
      filter.value,
      omitBy(it, (v) => v === undefined)
    )
  )
})
const isDelayedWorksDisabled = computed(() => {
  return (
    !hasDelayedWorksFilter.value &&
    (!!filter.value.minEndWorksDate ||
      !!filter.value.maxEndWorksDate ||
      !!filter.value.maxEstimatedEndWorksDate ||
      !!filter.value.minEstimatedEndWorksDate ||
      (!isEqual(filter.value.stepIds, []) && !isEqual(filter.value.stepIds, [50])) ||
      (!isEqual(filter.value.operationStatuses, []) && !isEqual(filter.value.operationStatuses, ['DOING'])))
  )
})

const clickBarChartElement = (event: any) => {
  const stepId: number = event.componentType === 'xAxis' ? Number(event.value) : Number(event.name)
  filter.value = {
    ...filter.value,
    stepIds: filter.value.stepIds
      ? filter.value.stepIds.includes(stepId)
        ? filter.value.stepIds.filter((it) => it !== stepId)
        : filter.value.stepIds.concat(stepId)
      : [stepId],
    operationStatuses: ['DOING'],
  }
}

/*
 * Dashboard Block - END
 */
const queryMapper = (query: LocationQuery): Record<string, unknown> => ({
  ...query,
  territoryIds: mapQueryToTable(query.territoryIds, true),
  stepIds: mapQueryToTable(query.stepIds, true),
  periodIds: mapQueryToTable(query.periodIds, true),
  valuationTypeIds: mapQueryToTable(query.valuationTypeIds, true),
  operationStatuses: mapQueryToTable(query.operationStatuses, false),
  standardizedOperationSheetIds: mapQueryToTable(query.standardizedOperationSheetIds, true),
  entityNavFullIds: isArray(query.entityNavFullIds)
    ? query.entityNavFullIds
    : query.entityNavFullIds
      ? [query.entityNavFullIds]
      : [],
  emmyFolderIds: mapQueryToTable(query.emmyFolderIds, true),
  instructorIds: mapQueryToTable(query.instructorIds, true),
  controlOrderNatures: mapQueryToTable(query.controlOrderNatures, false),
})

const { data, pageFilter, pageable, updatePageable, updateFilter, reload } = usePaginationInQuery<
  Operation,
  OperationFilter
>(
  (filter, pageable) =>
    filter.valuationMode
      ? operationApi.findAllWithValuations({ ...filter, ...dashboardFilter.value }, pageable).then((response) => {
          valuations.value = response.data.content.map((it) => it.valuations)
          return Promise.resolve({
            ...response,
            data: {
              ...response.data,
              content: response.data.content.map((it) => it.operation),
            },
          })
        })
      : operationApi.findAll({ ...filter, ...dashboardFilter.value }, pageable),
  {
    defaultPageFilter: { ...filter.value },
    defaultPageablePartial: {
      page: 0,
      size: 50,
      sort: defaultOrder,
    },
    queryToFilterMapper: queryMapper,
    saveFiltersName: 'OperationAllView',
  }
)

const reloadData = () => {
  console.debug('reloadData')
  return Promise.all([
    reload(),
    selectedOperation.value.value
      ? handleAxiosPromise(selectedOperation, simulationApi.findById(selectedOperation.value.value.id), {
          afterError: () =>
            snackbarStore.setError(
              selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
            ),
        })
      : Promise.resolve(),
  ])
}
// Headers
const operationDrawerRef = ref<typeof OperationDrawer | null>(null)
const drawer = ref<'filter' | 'detail' | 'column' | undefined>()
const selectedOperation = ref(emptyValue<Operation>())
const getOperationDetail = async (id: number) => {
  operationDrawerRef.value!.check(async () => {
    await handleAxiosPromise(selectedOperation, simulationApi.findById(id), {
      afterError: () =>
        snackbarStore.setError(
          selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
        ),
    })
  })
}

const handleFilterDrawer = () => {
  drawer.value = drawer.value === 'filter' ? undefined : 'filter'
}

const handleColumnManager = () => {
  drawer.value = drawer.value === 'column' ? undefined : 'column'
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    if (!isEqual(v, pageFilter.value)) {
      debounceFilter(v)
    }
  },
  {
    deep: true,
  }
)

watch(
  () => pageFilter.value,
  (v) => {
    if (!isEqual(v, filter.value)) {
      const tempFilter = cloneDeep(v) as any
      Object.keys(tempFilter).forEach((key) => {
        if (isArray(tempFilter[key]) && key !== 'entityNavFullIds' && (tempFilter[key] as any[]).length !== 0) {
          ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
            if (!Number.isNaN(parseInt(val))) {
              ;(tempFilter[key] as any[])[index] = parseInt(val)
            }
          })
        }
      })
      filter.value = tempFilter as any
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// Search
const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const onUpdateOperation = () => {
  reload()
}

const dialogStore = useDialogStore()

const disableChangeEntity = computed(() =>
  selection.value.length == 0
    ? (filter.value?.entityNavFullIds ?? []).length != 1
    : new Set(selection.value.map((i) => i.entity.id)).size > 1
)

const { exportOperationsLoading, exportOperations } = useExportOperation(pageFilter, selection, data, false)

const purgingOperations = ref(false)
const purgeOperations = async () => {
  if (!(pageFilter.value.stepIds?.every((it) => it >= 10 && it <= 40) ?? false)) {
    dialogStore.addAlert2({
      message: 'Vous devez sélectionner seulement des étapes entre 10 et 40 pour purger des opérations',
      title: 'Sélection étapes valides requises',
      maxWidth: '640px',
    })
    return
  }
  if (!isEqual(pageFilter.value.operationStatuses, ['DOING'])) {
    dialogStore.addAlert2({
      message: 'Vous ne pouvez purger seulement que sur les opérations en cours',
      title: 'Purge sur les opérations en cours seulement',
      maxWidth: '640px',
    })
    return
  }
  if ((pageFilter.value.minimumMonthsOld ?? 0) === 0) {
    dialogStore.addAlert2({
      message: "Vous devez saisir un minimum de durée de vétusté de l'opération",
      title: 'Nombre de mois requis',
      maxWidth: '640px',
    })
    return
  }

  if (
    await dialogStore.addAlert({
      message: "Vous allez purger un ensemble d'opérations. Confirmez vous bien votre action ?",
      title: 'Confirmation ',
      maxWidth: '640px',
    })
  ) {
    purgingOperations.value = true
    const allResponses = await Promise.allSettled<AxiosPromise[]>(
      data.value.value?.content.map((it) => operationApi.purge(it.id)) ?? []
    )
    const hasErrors = allResponses.some((it) => it.status === 'rejected' || it.value.status >= 400)
    if (hasErrors) {
      snackbarStore.setError("Certaines opérations n'ont pas pu être purgées.")
    } else {
      snackbarStore.setSuccess('Les opérations sélectionnées ont bien été purgées.')
    }
    purgingOperations.value = false
    reload()
  }
}

const changeEntityDialog = ref(false)

watch(changeEntityDialog, (v) => {
  if (v) {
    selectedEntity.value = ''
  }
})
const changeEntityMessage = computed(
  () => `
  Attention vous allez changer d'organisation ${
    selection.value.length ? selection.value.length : data.value.value?.totalElements
  } opérations.
  Vous ne pouvez changer des opérations d'organisation que si elles appartiennent à la même organisation.
  Si certaines opérations sont dans un regroupement, le regroupement et ses opérations changeront d'organisation.
`
)

const batchUpdateDialog = ref(false)
const batchUpdateResultDialog = ref(false)
const batchUpdating = ref(false)
const batchUpdatingError = ref<string>()
const batchUpdateRequest = ref<Partial<OperationRequest>>({})
const batchUpdateResults = shallowRef<[number, string][]>([])
const batchUpdateErrorOperationIds = shallowRef<[number, string, number][]>([])
const batchUpdateCurrentIndex = ref(0)
const batchUpdate = async () => {
  if (
    await dialogStore.addAlert({
      title: "Confirmation de modifications en masse d'opérations",
      message: "Vous allez faire une modification en masse d'opérations: êtes -vous certains de ce que vous faites ?",
      maxWidth: '640px',
    })
  ) {
    batchUpdating.value = true
    batchUpdatingError.value = undefined
    batchUpdateErrorOperationIds.value = []
    const totalElements = selection.value.length ? selection.value.length : (data.value.value?.totalElements ?? 0)
    const pageSize = 20
    const totalPages = Math.ceil(totalElements / pageSize)
    const results: [number, string][] = []
    batchUpdateCurrentIndex.value = 0
    console.debug('batch update', batchUpdateRequest.value)
    const errorCount: typeof batchUpdateErrorOperationIds.value = []
    try {
      for (let i = 0; i <= totalPages; i++) {
        const data = await operationApi
          .findAll(selection.value.length > 0 ? { operationIds: selection.value.map((it) => it.id) } : filter.value, {
            size: pageSize,
            page: i,
            sort: ['id'],
          })
          .catch((e) => {
            snackbarStore.setError('Erreur lors du chargement des opérations pour la MAJ en masse')
            throw e
          })
        for (const op of data.data.content) {
          const refOp = mapToOperationRequest(op)
          const currentOpRequest = { ...refOp, ...batchUpdateRequest.value }

          if (!isEqual(refOp, currentOpRequest)) {
            await simulationApi
              .updateSimulation(op.id, currentOpRequest)
              .then(() => {
                results.push([op.id, op.chronoCode])
              })
              .catch(() => {
                errorCount.push([op.id, op.chronoCode, op.stepId])
              })
          }
          batchUpdateCurrentIndex.value++
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_: unknown) {
      batchUpdatingError.value = 'Erreur lors de la mise à jour de masse d operations'
    }
    batchUpdateResults.value = results
    batchUpdateResultDialog.value = true
    batchUpdateDialog.value = false
    batchUpdating.value = false
    batchUpdateErrorOperationIds.value = errorCount
    reload()
  }
}
const arrayExpanded = ref(import.meta.env.VITE_FEATURE_TDB !== 'true')

const filterCount = computed((): number => {
  return (Object.keys(filter.value) as Array<keyof OperationFilter>).reduce<number>((acc, k): number => {
    let r: number = 0
    const value = filter.value[k]
    if (k === 'operationStatuses') {
      r = !isEqual(value, ['DOING']) && !isEqual(value, []) ? 1 : 0
    } else if (k === 'myRequests' || k === 'valuationMode' || k === 'warning') {
      r = value ? 1 : 0
    } else {
      r = value != null && (isBoolean(value) || !isEmpty(value)) ? 1 : 0
    }

    if (r > 0) {
      console.debug('count filter for ' + k, value, isEmpty(true), isEmpty(false))
    }

    return acc + r
  }, 0)
})

const onDoughnutsSelectionChanged = (a: any) => {
  if (isDelayedWorksDisabled.value && !hasDelayedWorksFilter.value) {
    return
  }
  const dataIndex = a.selected[0]?.dataIndex[0]
  if (dataIndex != null) {
    const data = delayedLimitsReferenceDates[dataIndex]
    updateFilter({
      ...filter.value,
      minEstimatedEndWorksDate: data.minEstimatedEndWorksDate,
      maxEstimatedEndWorksDate: data.maxEstimatedEndWorksDate,
      stepIds: [50],
      operationStatuses: ['DOING'],
    })
  } else {
    updateFilter({
      ...filter.value,
      minEstimatedEndWorksDate: '',
      maxEstimatedEndWorksDate: '',
      stepIds: [],
      operationStatuses:
        !isEqual(filter.value.operationStatuses, ['DOING']) && filter.value.operationStatuses?.length
          ? ['DOING']
          : filter.value.operationStatuses,
    })
  }
}

// Old - before tdb
const headerButtons = computed(() => {
  const res = []

  if (userStore.hasRole('ADMIN_PLUS')) {
    res.push({
      label: 'MAJ en masse',
      onClick: () => {
        batchUpdateDialog.value = true
      },
    })
    res.push({
      label: "Changer d'organisation",
      disabled: disableChangeEntity.value,
      onClick: () => {
        changeEntityDialog.value = true
      },
    })

    res.push({
      label: 'Purger les opérations',
      loading: exportOperationsLoading.value,
      onClick: purgeOperations,
    })
  }
  res.push({
    onClick: exportOperations,
    loading: exportOperationsLoading.value,
    label: 'Exporter',
  })
  res.push({
    label: 'Personnaliser',
    onClick: handleColumnManager,
  })
  return res
})
const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const hasFeatureTdb = ref(import.meta.env.VITE_FEATURE_TDB === 'true')
</script>
